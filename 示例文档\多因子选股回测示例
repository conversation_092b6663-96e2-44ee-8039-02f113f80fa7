#coding:gbk

"""
多因子选股回测策略示例
======================

策略概述：
本策略是一个基于沪深300成分股的多因子量化选股回测模型，通过技术指标筛选和因子评分来构建投资组合。

策略特点：
- 股票池：沪深300成分股
- 调仓频率：每20个交易日调仓一次（约1个月）
- 选股数量：最多选择10只股票
- 资金分配：每只股票分配10%的可用资金
- 因子评分：使用ATR和ADTM技术指标进行评分排序

交易逻辑：
1. 买入条件：股价突破20日最高价，进入买入备选池
2. 卖出条件：股价跌破60日均线，进入卖出备选池
3. 因子筛选：在买入备选中选择因子评分最优的前10只股票
4. 资金管理：等权重分配资金，每只股票10%

技术指标：
- ATR (Average True Range): 平均真实波幅，衡量价格波动性
- ADTM (动态买卖气指标): 衡量多空力量对比

注意事项：
- 本策略仅用于回测，不适用于实盘交易
- 需要预先生成扩展数据（ATR和ADTM指标）
- 手续费按万分之三计算
- 建议在充分回测验证后再考虑实盘应用

数据要求：
- 需要完整的沪深300成分股历史数据
- 扩展数据需要在补完成分股数据后生成
- 本模型使用VBA指标ATR和ADTM，命名为atr和adtm
"""

# 导入必要的数据处理和计算库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算和统计分析
import time          # 时间处理库，用于时间相关操作
import datetime      # 日期时间库，用于日期格式转换

def init(ContextInfo):
    """
    策略初始化函数

    功能：设置策略的基本参数、股票池和初始状态

    参数：
        ContextInfo: 策略运行环境对象，包含所有策略相关信息

    初始化内容：
    1. 设置股票池为沪深300成分股
    2. 初始化持仓记录和资金管理
    3. 设置权重分配和账户信息
    4. 初始化盈亏统计变量

    关键变量说明：
        s: 沪深300成分股列表
        holdings: 各股票持仓数量字典
        weight: 资金分配权重列表
        buypoint: 买入价格记录
        money: 当前可用资金
        profit: 累计盈亏
    """
    # 获取沪深300指数成分股列表
    # '000300.SH'是沪深300指数的代码
    ContextInfo.s = ContextInfo.get_sector('000300.SH')

    # 设置股票池，订阅沪深300成分股的行情数据
    ContextInfo.set_universe(ContextInfo.s)

    # 初始化交易日计数器
    ContextInfo.day = 0

    # 初始化持仓记录字典，所有股票初始持仓为0
    # 格式：{股票代码: 持仓手数}
    ContextInfo.holdings = {i: 0 for i in ContextInfo.s}

    # 设置资金分配权重，10只股票各分配10%的资金
    # 可根据需要调整权重分配策略
    ContextInfo.weight = [0.1] * 10

    # 初始化买入价格记录字典，用于计算盈亏
    # 格式：{股票代码: 买入价格}
    ContextInfo.buypoint = {}

    # 初始化可用资金为初始资本
    ContextInfo.money = ContextInfo.capital

    # 初始化累计盈亏为0
    ContextInfo.profit = 0

    # 设置回测账户ID
    ContextInfo.accountID = 'testS'

def handlebar(ContextInfo):
    """
    K线数据处理函数（策略核心逻辑）

    功能：执行多因子选股和组合调仓策略

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 检查是否到达调仓时点（每20个交易日）
    2. 获取买入卖出信号
    3. 对买入备选股票进行因子评分
    4. 选择评分最优的前10只股票
    5. 执行卖出操作（清仓不符合条件的股票）
    6. 执行买入操作（建仓新选中的股票）
    7. 更新资金和盈亏统计
    8. 绘制收益率曲线

    调仓逻辑：
    - 调仓频率：每20个交易日（约1个月）
    - 选股数量：最多10只股票
    - 资金分配：等权重分配（每只10%）
    - 手续费：万分之三
    """

    # === 第一步：初始化变量和获取基础数据 ===

    # 初始化因子评分字典
    rank1 = {}       # ATR因子评分
    rank2 = {}       # ADTM因子评分
    rank_total = {}  # 综合因子评分
    tmp_stock = {}   # 最终选中的股票池

    # 获取当前K线位置（交易日序号）
    d = ContextInfo.barpos

    # 获取当前开盘价数据，用于交易执行
    price = ContextInfo.get_history_data(1, '1d', 'open', 3)

    # === 第二步：调仓时点判断 ===

    # 调仓条件：
    # 1. d > 60: 确保有足够的历史数据进行计算
    # 2. d % 20 == 0: 每20个交易日调仓一次
    if d > 60 and d % 20 == 0:

        # 获取当前日期并输出
        nowDate = timetag_to_datetime(ContextInfo.get_bar_timetag(d), '%Y%m%d')
        print(nowDate)

        # === 第三步：获取买卖信号 ===

        # 调用signal函数获取买入和卖出备选股票
        buys, sells = signal(ContextInfo)

        # 初始化订单字典，记录每只股票的买入手数
        order = {}

        # === 第四步：因子评分和股票筛选 ===

        # 对所有买入备选股票进行因子评分
        for k in list(buys.keys()):
            if buys[k] == 1:  # 如果股票在买入备选中

                # 计算ATR因子评分
                # ext_data_rank: 获取扩展数据排名
                # 'atr': ATR指标名称
                # k[-2:]+k[0:6]: 转换股票代码格式
                # 0: 当前时点
                rank1[k] = ext_data_rank('atr', k[-2:]+k[0:6], 0, ContextInfo)

                # 计算ADTM因子评分
                rank2[k] = ext_data_rank('adtm', k[-2:]+k[0:6], 0, ContextInfo)

                # 计算综合因子评分
                # 注意：因子权重需要根据实际情况调整
                # 此处仅使用ATR因子，权重为1.0
                rank_total[k] = 1.0 * rank1[k]
                print(1111111, rank1[k])

        # 按综合因子评分排序，选择评分最优的股票
        tmp = sorted(list(rank_total.items()), key=lambda item: item[1])

        # 选择前10只股票，如果不足10只则全选
        if len(tmp) >= 10:
            tmp_stock = {i[0] for i in tmp[:10]}
        else:
            tmp_stock = {i[0] for i in tmp}

        # 更新买入信号：只保留最终选中的股票
        for k in list(buys.keys()):
            if k not in tmp_stock:
                buys[k] = 0

        # === 第五步：执行交易操作 ===

        if tmp_stock:
            print('stock pool:', tmp_stock)

            # --- 执行卖出操作 ---
            for k in ContextInfo.s:
                # 卖出条件：当前有持仓 且 在卖出备选中
                if ContextInfo.holdings[k] > 0 and sells[k] == 1:
                    print('ready to sell')

                    # 执行卖出订单（负数表示卖出）
                    order_shares(k, -ContextInfo.holdings[k]*100, 'fix', price[k][-1], ContextInfo, ContextInfo.accountID)

                    # 更新可用资金（卖出收入 - 手续费）
                    # 手续费按万分之三计算
                    ContextInfo.money += price[k][-1] * ContextInfo.holdings[k] * 100 - 0.0003*ContextInfo.holdings[k]*100*price[k][-1]

                    # 更新累计盈亏（卖出价格 - 买入价格）* 股数 - 手续费
                    ContextInfo.profit += (price[k][-1] - ContextInfo.buypoint[k]) * ContextInfo.holdings[k] * 100 - 0.0003*ContextInfo.holdings[k]*100*price[k][-1]

                    print(k)

                    # 清空持仓记录
                    ContextInfo.holdings[k] = 0

            # --- 计算资金分配 ---
            # 根据权重分配可用资金给选中的股票
            ContextInfo.money_distribution = {k: i*ContextInfo.money for (k, i) in zip(tmp_stock, ContextInfo.weight)}

            # --- 执行买入操作 ---
            for k in tmp_stock:
                # 买入条件：当前无持仓 且 在买入备选中
                if ContextInfo.holdings[k] == 0 and buys[k] == 1:
                    print('ready to buy')

                    # 计算买入手数：分配资金 / 股价 / 100（向下取整）
                    order[k] = int(ContextInfo.money_distribution[k] / (price[k][-1])) / 100

                    # 执行买入订单
                    order_shares(k, order[k]*100, 'fix', price[k][-1], ContextInfo, ContextInfo.accountID)

                    # 记录买入价格，用于后续盈亏计算
                    ContextInfo.buypoint[k] = price[k][-1]

                    # 更新可用资金（扣除买入成本和手续费）
                    ContextInfo.money -= price[k][-1] * order[k] * 100 - 0.0003*order[k]*100*price[k][-1]

                    # 更新累计盈亏（扣除买入手续费）
                    ContextInfo.profit -= 0.0003*order[k]*100*price[k][-1]

                    print(k)

                    # 更新持仓记录
                    ContextInfo.holdings[k] = order[k]

            # 输出当前资金状况
            print(ContextInfo.money, ContextInfo.profit, ContextInfo.capital)

    # === 第六步：绘制收益率曲线 ===

    # 计算收益率（累计盈亏 / 初始资本）
    profit = ContextInfo.profit / ContextInfo.capital

    # 如果不是回测模式，绘制收益率曲线
    if not ContextInfo.do_back_test:
        ContextInfo.paint('profit_ratio', profit, -1, 0)
		

def signal(ContextInfo):
    """
    交易信号生成函数

    功能：基于技术指标生成买入和卖出信号

    参数：
        ContextInfo: 策略运行环境对象

    返回值：
        buy: 买入信号字典，{股票代码: 信号值}，1表示买入，0表示不买入
        sell: 卖出信号字典，{股票代码: 信号值}，1表示卖出，0表示不卖出

    信号逻辑：
    1. 买入信号：股价突破20日最高价（动量突破策略）
    2. 卖出信号：股价跌破60日均线（趋势跟踪策略）

    技术指标：
    - 20日最高价：短期阻力位突破
    - 60日均线：中长期趋势判断

    数据要求：
    - 至少需要62个交易日的历史数据
    - 确保数据完整性和连续性
    """

    # === 第一步：初始化信号字典 ===

    # 初始化买入信号字典，所有股票初始为0（不买入）
    buy = {i: 0 for i in ContextInfo.s}

    # 初始化卖出信号字典，所有股票初始为0（不卖出）
    sell = {i: 0 for i in ContextInfo.s}

    # === 第二步：获取历史价格数据 ===

    # 获取22日最高价数据（用于计算20日最高价）
    # 参数说明：
    # 22: 获取22个交易日的数据
    # '1d': 日线数据
    # 'high': 最高价字段
    # 3: 数据精度参数
    data_high = ContextInfo.get_history_data(22, '1d', 'high', 3)

    # 获取最近2日的最高价数据（用于判断突破）
    data_high_pre = ContextInfo.get_history_data(2, '1d', 'high', 3)

    # 获取62日收盘价数据（用于计算60日均线）
    data_close60 = ContextInfo.get_history_data(62, '1d', 'close', 3)

    # === 第三步：遍历股票池生成交易信号 ===

    for k in ContextInfo.s:
        # 检查股票是否有足够的历史数据
        if k in data_close60:

            # 验证数据完整性：确保各个时间序列的数据长度正确
            if (len(data_high_pre[k]) == 2 and
                len(data_high[k]) == 22 and
                len(data_close60[k]) == 62):

                # === 买入信号判断：突破20日最高价 ===

                # 获取前一日的最高价
                yesterday_high = data_high_pre[k][-2]

                # 计算20日最高价（排除当前日）
                twenty_day_high = max(data_high[k][:-2])

                # 买入条件：前一日最高价突破20日最高价
                # 这表示股价创出近期新高，可能开始新的上涨趋势
                if yesterday_high > twenty_day_high:
                    buy[k] = 1  # 标记为买入备选

                # === 卖出信号判断：跌破60日均线 ===

                # 计算60日均线（排除当前日）
                sixty_day_avg = np.mean(data_close60[k][:-2])

                # 卖出条件：前一日最高价低于60日均线
                # 这表示股价跌破中长期趋势线，可能进入下跌趋势
                elif yesterday_high < sixty_day_avg:
                    sell[k] = 1  # 标记为卖出备选

    # 返回买入和卖出信号字典
    return buy, sell

