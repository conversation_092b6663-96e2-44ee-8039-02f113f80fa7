#coding:gbk

"""
QMT历史数据获取工具
===================

功能概述：
本工具用于从QMT平台下载和获取各类金融产品的历史数据，支持股票、期货、期权、可转债等多种品种。

主要特点：
- 支持多种数据周期：tick、分钟、日线等
- 支持多种金融品种：股票、期货、期权、可转债
- 自动下载管理：检查数据完整性并自动下载缺失数据
- 进度显示：实时显示下载进度
- 数据验证：下载完成后验证数据有效性

数据周期支持：
- tick: 逐笔成交数据
- 1m, 3m, 5m, 15m, 30m: 分钟级K线数据
- 1d: 日线数据
- 其他自定义周期

品种支持：
- 股票：如 000001.SZ, 600000.SH
- 期货：如 rb2401.SF, FG403.ZF
- 期权：如 HO2310-P-2500.IF
- 可转债：如 110052.SH

使用场景：
- 策略回测前的数据准备
- 历史数据分析和研究
- 数据完整性检查和补充
- 批量数据下载

注意事项：
- 下载大量数据时需要足够的存储空间
- tick数据文件较大，下载时间较长
- 建议在网络稳定的环境下进行数据下载
- 下载完成后需要等待一定时间确保数据写入完成
"""

# 导入必要的数据处理库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算
import time          # 时间库，用于等待和延时操作

def init(C):
    """
    历史数据获取初始化函数

    功能：配置数据下载参数并执行数据获取操作

    参数：
        C: ContextInfo对象，包含策略运行环境信息

    配置项说明：
        start_date: 开始下载日期，格式"YYYYMMDD"
        end_date: 结束下载日期，空字符串表示到最新
        period: 数据周期（tick/1m/5m/1d等）
        need_download: 是否需要下载数据（1=下载，0=不下载）
        code_list: 要下载的证券代码列表

    执行流程：
    1. 设置下载参数
    2. 构建证券代码列表
    3. 执行数据下载（如果需要）
    4. 等待下载完成
    5. 获取并验证数据
    6. 显示数据信息
    """

    # === 第一步：设置数据下载参数 ===

    # 设置下载开始日期
    # 格式："YYYYMMDD"
    # 示例：'20231001' - 从2023年10月1日开始下载
    # 注意：如果设置为空字符串""，则进行全量下载
    start_date = '20250701'  # 从2025年7月1日开始下载

    # 设置下载结束日期
    # 格式："YYYYMMDD"
    # 空字符串表示下载到最新数据
    end_date = ""

    # 设置数据周期
    # 支持的周期：
    # - "tick": 逐笔成交数据（最详细，文件最大）
    # - "1m", "3m", "5m", "15m", "30m": 分钟级K线
    # - "1d": 日线数据
    period = "tick"

    # 设置是否需要下载数据
    # 1: 需要下载（当本地数据不完整或缺失时）
    # 0: 不下载（仅使用本地已有数据）
    need_download = 1

    # === 第二步：构建证券代码列表 ===

    # 不同品种的代码示例（注释掉的为其他品种示例）
    # code_list = ["110052.SH"]                    # 可转债示例
    # code_list = ["rb2401.SF", "FG403.ZF"]        # 期货列表示例
    # code_list = ["HO2310-P-2500.IF"]             # 期权列表示例

    # 构建当前选择的股票代码
    C.stock = C.stockcode + '.' + C.market
    code_list = [C.stock]  # 股票列表（当前策略选择的股票）

    # === 第三步：执行数据下载 ===

    # 根据need_download标志决定是否下载数据
    if need_download:
        print(f"开始下载历史数据...")
        print(f"品种: {code_list}")
        print(f"周期: {period}")
        print(f"时间范围: {start_date} 到 {end_date if end_date else '最新'}")

        # 调用自定义下载函数
        my_download(code_list, period, start_date, end_date)

    # === 第四步：等待下载完成 ===

    # 等待10秒确保数据下载和写入完成
    # 对于大量数据，可能需要更长的等待时间
    print("等待数据下载完成...")
    time.sleep(10)

    # === 第五步：获取和验证数据 ===

    # 使用QMT API获取历史数据
    # 参数说明：
    # []: 字段列表，空列表表示获取所有字段
    # code_list: 证券代码列表
    # period: 数据周期
    # start_time/end_time: 时间范围
    # dividend_type: 复权类型（"back_ratio"=后复权）
    data = C.get_market_data_ex([], code_list,
                               period=period,
                               start_time=start_date,
                               end_time=end_date,
                               dividend_type="back_ratio")

    # === 第六步：显示数据信息 ===

    # 显示获取到的行情数据
    print("\n=== 历史数据获取结果 ===")
    print(data)

    # 显示合约详细信息
    print("\n=== 合约信息 ===")
    print(C.get_instrumentdetail(code_list[0]))

    # 数据统计信息
    if not data.empty:
        for code in code_list:
            if code in data:
                df = data[code]
                print(f"\n{code} 数据统计:")
                print(f"数据条数: {len(df)}")
                print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
                print(f"数据字段: {list(df.columns)}")
    else:
        print("警告：未获取到有效数据，请检查代码和时间范围设置")

def hanldebar(C):
    """
    K线处理函数（空实现）

    功能：本工具主要用于数据下载，不需要K线处理逻辑

    参数：
        C: ContextInfo对象

    说明：
        - 本函数为必需函数，但在数据下载工具中不执行任何操作
        - 实际的数据获取和处理都在init函数中完成
    """
    return

def my_download(stock_list, period, start_date='', end_date=''):
    """
    批量下载历史数据函数

    功能：批量下载指定证券列表的历史数据，并显示下载进度

    参数：
        stock_list: 证券代码列表，如['000001.SZ', '600000.SH']
        period: 数据周期，如'tick', '1m', '5m', '1d'等
        start_date: 开始日期，格式'YYYYMMDD'，默认为空（全量下载）
        end_date: 结束日期，格式'YYYYMMDD'，默认为空（到最新）

    周期标准化：
        - 包含'd'的周期 → '1d'（日线）
        - 包含'm'且数字<5 → '1m'（1分钟）
        - 包含'm'且数字≥5 → '5m'（5分钟）
        - 'tick' → 保持不变（逐笔数据）

    功能特点：
        - 自动标准化数据周期格式
        - 实时显示下载进度
        - 逐个下载，确保稳定性
        - 错误处理和异常提示

    使用示例：
        my_download(['000001.SZ'], 'tick', '20240101', '20241231')
        my_download(['rb2401.SF'], '1m', '20240101')

    注意事项：
        - tick数据下载时间较长，请耐心等待
        - 网络不稳定时可能需要重试
        - 大量数据下载建议分批进行
    """

    # === 第一步：数据周期标准化 ===

    # 根据输入的周期字符串，标准化为QMT支持的格式
    if "d" in period:
        # 日线数据：任何包含'd'的周期都转换为'1d'
        period = "1d"
        print(f"周期标准化: {period} → 1d (日线)")

    elif "m" in period:
        # 分钟线数据：根据数字大小选择合适的基础周期
        try:
            # 提取周期中的数字部分
            minute_num = int(period[0])

            if minute_num < 5:
                # 小于5分钟的周期使用1分钟基础数据
                period = "1m"
                print(f"周期标准化: 分钟线 → 1m (1分钟基础)")
            else:
                # 5分钟及以上的周期使用5分钟基础数据
                period = "5m"
                print(f"周期标准化: 分钟线 → 5m (5分钟基础)")

        except ValueError:
            # 如果无法提取数字，默认使用1分钟
            period = "1m"
            print(f"周期解析失败，默认使用: 1m")

    elif "tick" == period:
        # tick数据保持不变
        print(f"使用tick数据（逐笔成交）")
        pass

    else:
        # 不支持的周期格式，抛出异常
        error_msg = f"不支持的数据周期: {period}"
        print(f"错误: {error_msg}")
        raise KeyboardInterrupt(error_msg)

    # === 第二步：批量下载处理 ===

    # 初始化进度计数器
    n = 1
    num = len(stock_list)

    print(f"\n开始批量下载，共{num}个品种")
    print(f"数据周期: {period}")
    print(f"时间范围: {start_date if start_date else '全量'} 到 {end_date if end_date else '最新'}")
    print("-" * 50)

    # 逐个下载证券数据
    for i in stock_list:

        # 显示当前下载进度
        print(f"正在下载 {n}/{num}: {i}")

        try:
            # 调用QMT底层下载函数
            # download_history_data是QMT提供的历史数据下载API
            download_history_data(i, period, start_date, end_date)

            print(f"✓ {i} 下载完成")

        except Exception as e:
            # 捕获下载过程中的异常
            print(f"✗ {i} 下载失败: {e}")

        # 更新进度计数器
        n += 1

    # === 第三步：下载完成提示 ===

    print("-" * 50)
    print("批量下载任务完成！")
    print(f"成功处理 {num} 个品种")
    print("请等待数据写入完成后再进行数据获取操作")
