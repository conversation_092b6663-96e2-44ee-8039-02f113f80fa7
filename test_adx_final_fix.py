#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ADX最终修复版本
"""

def test_adx_final_fix():
    """测试最终修复的ADX计算"""
    try:
        print("=== 测试ADX最终修复版本 ===")
        
        import sys
        import os
        import numpy as np
        
        # 添加路径
        sys.path.append(os.path.join(os.getcwd(), '框架'))
        
        # 导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(ADX_N=14, ADX_M=7)
        print(f"📊 检测器创建成功，ADX_N={detector.ADX_N}, ADX_M={detector.ADX_M}")
        
        # 测试场景1：最小数据量
        print("\n📊 测试场景1: 最小数据量")
        min_count = detector.ADX_N + 1  # 15根K线
        
        # 生成明显上升趋势数据
        base_price = 10.0
        trend = 0.03  # 每根K线上涨3%
        
        highs = [base_price * (1 + trend * i) + 0.05 for i in range(min_count)]
        lows = [base_price * (1 + trend * i) - 0.05 for i in range(min_count)]
        closes = [base_price * (1 + trend * i) for i in range(min_count)]
        
        print(f"   数据长度: {len(highs)}根K线")
        print(f"   价格范围: {closes[0]:.2f} ~ {closes[-1]:.2f}")
        print(f"   总涨幅: {(closes[-1]/closes[0]-1)*100:.1f}%")
        
        # 测试ADX计算
        print("   🔄 开始ADX计算...")
        adx_result = detector.calculate_ADX(highs, lows, closes)
        
        print(f"   ✅ ADX计算成功")
        print(f"   ADX结果长度: {len(adx_result)}")
        print(f"   ADX数据类型: {type(adx_result)}")
        print(f"   ADX范围: {adx_result.min():.2f} ~ {adx_result.max():.2f}")
        print(f"   ADX最后值: {adx_result[-1]:.2f}")
        print(f"   ADX非零值数量: {np.count_nonzero(adx_result)}")
        
        if adx_result[-1] > 0:
            print("   ✅ ADX计算正常，有非零值")
            if adx_result[-1] > 25:
                print(f"   ✅ ADX显示强趋势: {adx_result[-1]:.2f} > 25")
            else:
                print(f"   📊 ADX显示弱趋势: {adx_result[-1]:.2f} < 25")
        else:
            print("   ⚠️ ADX为0，可能趋势不够强或数据不足")
        
        # 测试场景2：更多数据
        print("\n📊 测试场景2: 更多数据")
        more_count = 30
        
        highs_more = [base_price * (1 + trend * i) + 0.05 for i in range(more_count)]
        lows_more = [base_price * (1 + trend * i) - 0.05 for i in range(more_count)]
        closes_more = [base_price * (1 + trend * i) for i in range(more_count)]
        
        print(f"   数据长度: {more_count}根K线")
        adx_more = detector.calculate_ADX(highs_more, lows_more, closes_more)
        
        print(f"   ADX最后值: {adx_more[-1]:.2f}")
        print(f"   ADX>25: {'✅' if adx_more[-1] > 25 else '❌'}")
        print(f"   ADX>40: {'✅' if adx_more[-1] > 40 else '❌'}")
        
        # 测试场景3：不同数据类型
        print("\n📊 测试场景3: 不同数据类型")
        
        # 测试numpy数组
        highs_np = np.array(highs_more, dtype=np.float64)
        lows_np = np.array(lows_more, dtype=np.float64)
        closes_np = np.array(closes_more, dtype=np.float64)
        
        adx_np = detector.calculate_ADX(highs_np, lows_np, closes_np)
        print(f"   numpy数组输入: ADX最后值 = {adx_np[-1]:.2f}")
        
        # 验证结果一致性
        diff = abs(adx_more[-1] - adx_np[-1])
        print(f"   结果一致性: 差异 = {diff:.6f}")
        if diff < 1e-6:
            print("   ✅ 不同输入类型结果一致")
        
        # 测试场景4：综合信号检测
        print("\n📊 测试场景4: 综合信号检测")
        
        # 构造K线数据
        merged_klines = []
        for i in range(more_count):
            kline = {
                'open': closes_more[i] - 0.02,
                'high': highs_more[i],
                'low': lows_more[i],
                'close': closes_more[i],
                'volume': 1000 + i * 10
            }
            merged_klines.append(kline)
        
        print("   🔄 开始综合信号检测...")
        result = detector.get_comprehensive_signals(merged_klines)
        
        print(f"   检测状态: {result['status']}")
        
        if result['status'] == 'success':
            indicators = result.get('indicators', {})
            conditions = result.get('conditions', {})
            
            adx_value = indicators.get('ADX', 0)
            print(f"   ADX指标值: {adx_value:.2f}")
            print(f"   强趋势确认: {conditions.get('强趋势确认', False)} (ADX>40)")
            
            if adx_value > 0:
                print("   ✅ ADX在综合信号中计算正常")
            else:
                print("   ⚠️ ADX在综合信号中为0")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resistance_and_adx_together():
    """测试阻力线和ADX一起工作"""
    try:
        print("\n=== 测试阻力线和ADX协同工作 ===")
        
        import sys
        import os
        import numpy as np
        
        # 添加路径
        sys.path.append(os.path.join(os.getcwd(), '框架'))
        
        # 导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector()
        
        # 构造理想的买入场景数据
        print("📊 构造理想买入场景:")
        
        test_count = 60  # 足够的数据
        
        # 构造先下跌后上涨的数据（容易产生背离和超卖）
        prices_down = np.linspace(12.0, 9.0, 30)   # 下跌阶段
        prices_up = np.linspace(9.0, 11.5, 30)    # 反弹阶段
        all_closes = np.concatenate([prices_down, prices_up])
        
        # 构造对应的高低价和成交量
        all_highs = all_closes + 0.1 + np.random.random(test_count) * 0.05
        all_lows = all_closes - 0.1 - np.random.random(test_count) * 0.05
        all_volumes = 1000 + np.random.random(test_count) * 500
        
        print(f"   数据特征: {test_count}根K线")
        print(f"   价格走势: {all_closes[0]:.2f} → {all_closes[29]:.2f} → {all_closes[-1]:.2f}")
        
        # 构造K线数据
        merged_klines = []
        for i in range(test_count):
            kline = {
                'open': all_closes[i] - 0.02,
                'high': all_highs[i],
                'low': all_lows[i],
                'close': all_closes[i],
                'volume': all_volumes[i]
            }
            merged_klines.append(kline)
        
        # 测试综合信号
        print("\n🔄 开始综合信号检测...")
        result = detector.get_comprehensive_signals(merged_klines)
        
        print(f"📊 检测结果:")
        print(f"   状态: {result['status']}")
        
        if result['status'] == 'success':
            indicators = result.get('indicators', {})
            conditions = result.get('conditions', {})
            
            print(f"\n📊 关键指标值:")
            adx_value = indicators.get('ADX', 0)
            print(f"   ADX: {adx_value:.2f}")
            print(f"   CMF: {indicators.get('CMF', 0):.4f}")
            print(f"   BIAS: {indicators.get('BIAS', 0):.2f}%")
            print(f"   SKDJ_K: {indicators.get('K', 0):.2f}")
            print(f"   SKDJ_D: {indicators.get('D', 0):.2f}")
            print(f"   阻力线: {indicators.get('resistance_line', 0):.3f}")
            print(f"   当前价格: {indicators.get('current_price', 0):.2f}")
            
            print(f"\n📊 条件检查:")
            print(f"   SKDJ超卖: {conditions.get('SKDJ超卖', False)}")
            print(f"   双重背离: {conditions.get('双重背离', False)}")
            print(f"   强趋势确认: {conditions.get('强趋势确认', False)} (ADX>40)")
            print(f"   突破确认: {conditions.get('突破确认', False)}")
            print(f"   买入信号: {result.get('buy_signal', False)}")
            
            # 验证修复效果
            success_count = 0
            total_checks = 2
            
            if adx_value > 0:
                print("✅ ADX修复成功，不再为0")
                success_count += 1
            else:
                print("❌ ADX仍为0")
            
            resistance = indicators.get('resistance_line', 0)
            current_price = indicators.get('current_price', 0)
            if resistance > 0 and current_price > 0:
                gap = (resistance - current_price) / current_price * 100
                print(f"✅ 阻力线计算正常，当前差距: {gap:.2f}%")
                success_count += 1
            else:
                print("❌ 阻力线计算异常")
            
            print(f"\n📊 修复验证: {success_count}/{total_checks} 通过")
            
            if success_count == total_checks:
                print("🎉 阻力线和ADX修复完全成功！")
            
        return True
        
    except Exception as e:
        print(f"❌ 协同测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始ADX最终修复测试...")
    
    success_count = 0
    total_tests = 2
    
    if test_adx_final_fix():
        success_count += 1
    
    if test_resistance_and_adx_together():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 ADX数组索引问题完全修复！")
        print("\n📋 修复总结:")
        print("1. ✅ 避免数组索引错误：使用循环代替数组切片")
        print("2. ✅ 数据类型一致：确保所有计算使用float64")
        print("3. ✅ 手动构建数组：避免concatenate操作的类型冲突")
        print("4. ✅ 逐步计算：每个步骤都安全可控")
        print("5. ✅ ADX和阻力线：两个关键问题都已解决")
    else:
        print("❌ 部分测试失败，需要进一步检查")
