#coding:gbk

"""
沪深300指数增强回测策略
========================

策略概述：
本策略是一个基于沪深300指数的增强型被动投资策略，通过动态调整成分股权重来获取超额收益。

策略原理：
1. 基础配置：以0.8倍权重跟踪沪深300指数中权重大于0.35%的成分股
2. 动态调整：根据个股短期表现调整权重配比
3. 权重范围：0.6倍（弱势）- 0.8倍（中性）- 1.0倍（强势）

选股标准：
- 股票池：沪深300成分股
- 权重筛选：仅选择权重大于0.35%的成分股
- 初始权重：每只股票按指数权重的0.8倍配置

调仓逻辑：
1. 强势股判断：连续5天上涨 → 权重调至1.0倍（0.8+0.2）
2. 弱势股判断：连续5天下跌 → 权重调至0.6倍（0.8-0.2）
3. 中性股票：维持0.8倍权重不变

策略特点：
- 被动跟踪：基于指数权重进行配置
- 主动增强：根据短期动量调整权重
- 风险控制：权重调整幅度有限（±0.2）
- 适合回测：策略逻辑清晰，便于验证

适用环境：
- 回测模式（非实盘交易）
- 沪深300指数日线数据
- 需要完整的成分股权重数据

注意事项：
- 本策略仅用于回测验证，不适用于实盘交易
- 需要确保有足够的历史数据支持
- 权重调整可能导致较高的换手率
- 建议结合交易成本进行优化
"""

# 导入数值计算库，用于数组运算和统计分析
import numpy as np

def init(ContextInfo):
    """
    策略初始化函数

    功能：设置指数增强策略的基本参数和股票池

    参数：
        ContextInfo: 策略运行环境对象

    初始化内容：
    1. 获取沪深300成分股列表
    2. 筛选权重大于0.35%的成分股
    3. 构建股票权重字典
    4. 设置初始权重比例
    5. 配置回测账户

    关键变量：
        stock300_weight: 成分股权重字典
        ratio: 初始权重比例（0.8）
        index_code: 指数代码
    """

    # === 第一步：获取沪深300成分股 ===

    # 获取沪深300指数的所有成分股列表
    stock300 = ContextInfo.get_stock_list_in_sector('沪深300')

    # 初始化成分股权重字典
    ContextInfo.stock300_weight = {}

    # 初始化筛选后的股票列表和权重列表
    stock300_symbol = []      # 符合条件的股票代码列表
    stock300_weightlist = []  # 对应的权重列表

    # 构建完整的指数代码
    ContextInfo.index_code = ContextInfo.stockcode + "." + ContextInfo.market

    # === 第二步：筛选和处理成分股 ===

    for key in stock300:
        # 获取该股票在指数中的权重（百分比形式）
        weight_in_index = ContextInfo.get_weight_in_index(ContextInfo.index_code, key) / 100

        # 筛选条件：仅保留权重大于0.35%的成分股
        # 这样做的目的是：
        # 1. 减少小权重股票的交易成本
        # 2. 聚焦于对指数影响较大的核心成分股
        # 3. 简化组合管理复杂度
        if weight_in_index > 0.0035:
            stock300_symbol.append(key)
            ContextInfo.stock300_weight[key] = weight_in_index
            stock300_weightlist.append(weight_in_index)

    # 输出筛选结果统计信息
    print('选择的成分股权重总和为: ', np.sum(stock300_weightlist))

    # 设置策略的股票池为筛选后的成分股
    ContextInfo.set_universe(stock300_symbol)

    # === 第三步：设置策略参数 ===

    # 设置资产配置的初始权重比例
    # 0.8表示以指数权重的80%作为基础配置
    # 后续可在0.6-1.0之间动态调整（±0.2的调整空间）
    ContextInfo.ratio = 0.8

    # 设置回测账户ID
    ContextInfo.accountid = "testS"

def handlebar(ContextInfo):
    """
    指数增强策略核心处理函数

    功能：执行基于动量的权重调整和组合再平衡

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 获取当前时间和历史价格数据
    2. 计算当前持仓市值和可用资金
    3. 对无持仓股票按初始权重建仓
    4. 对有持仓股票根据动量信号调整权重
    5. 统计交易次数并绘制监控图表

    权重调整逻辑：
    - 连续5天上涨：权重从0.8调至1.0（增加0.2）
    - 连续5天下跌：权重从0.8调至0.6（减少0.2）
    - 其他情况：维持0.8权重不变

    风险控制：
    - 权重调整幅度限制在±0.2
    - 基于指数权重进行配置，避免过度集中
    - 仅对符合条件的成分股进行操作
    """

    # === 第一步：初始化和基础数据获取 ===

    # 初始化交易统计计数器
    buy_sum = 0   # 买入操作次数
    sell_sum = 0  # 卖出操作次数

    # 获取当前K线位置和时间信息
    index = ContextInfo.barpos
    realtimetag = ContextInfo.get_bar_timetag(index)
    print(timetag_to_datetime(realtimetag, '%Y%m%d %H:%M:%S'))

    # 获取7日历史收盘价数据（用于计算5日动量）
    # 需要7天数据：前5天用于计算动量，第6天作为基准，第7天为当前价格
    dict_close = ContextInfo.get_history_data(7, '1d', 'close', 3)

    # === 第二步：计算当前资产状况 ===

    # 初始化持仓市值
    holdvalue = 0

    # 获取当前持仓信息（手数）
    holdings = get_holdings(ContextInfo.accountid, "STOCK")

    # 获取剩余可用资金
    surpluscapital = get_avaliablecost(ContextInfo.accountid, "STOCK")

    # 计算当前持仓总市值
    for stock in ContextInfo.stock300_weight:
        if stock in holdings:
            if len(dict_close[stock]) == 7:
                # 使用前一日收盘价计算持仓市值
                # holdings[stock]是手数，需要乘以100转换为股数
                holdvalue += dict_close[stock][-2] * holdings[stock]

    # === 第三步：执行交易逻辑 ===

    for stock in ContextInfo.stock300_weight:

        # --- 情况1：无持仓股票的初始建仓 ---
        if stock not in holdings and stock in list(dict_close.keys()):
            if len(dict_close[stock]) == 7:
                # 获取前一日收盘价作为交易价格
                pre_close = dict_close[stock][-1]

                # 计算初始建仓数量（手数）
                # 公式：指数权重 × 总资产 × 初始权重比例 ÷ 股价 ÷ 100
                buy_num = int(ContextInfo.stock300_weight[stock] * (holdvalue + surpluscapital) * ContextInfo.ratio / pre_close / 100)

                # 执行买入订单（转换为股数）
                order_shares(stock, buy_num*100, 'fix', pre_close, ContextInfo, ContextInfo.accountid)
                buy_sum += 1

        # --- 情况2：有持仓股票的动量调整 ---
        elif stock in list(dict_close.keys()):
            if len(dict_close[stock]) == 7:

                # 计算过去5天的价格变化（动量指标）
                # 比较第2-6天与第1-5天的价格差异
                diff = np.array(dict_close[stock][1:6]) - np.array(dict_close[stock][:-2])

                # 获取交易价格
                pre_close = dict_close[stock][-1]

                # 计算目标持仓数量（手数）
                # 强势股目标：权重+0.2（即1.0倍权重）
                buytarget_num = int(ContextInfo.stock300_weight[stock] * (holdvalue + surpluscapital) * (ContextInfo.ratio + 0.2) / pre_close / 100)

                # 弱势股目标：权重-0.2（即0.6倍权重）
                selltarget_num = int(ContextInfo.stock300_weight[stock] * (holdvalue + surpluscapital) * (ContextInfo.ratio - 0.2) / pre_close / 100)

                # --- 强势股处理：连续5天上涨 ---
                if all(diff > 0) and holdings[stock] < buytarget_num:
                    # 计算需要增加的持仓
                    buy_num = buytarget_num - holdings[stock]

                    # 执行加仓操作
                    order_shares(stock, buy_num*100, 'fix', pre_close, ContextInfo, ContextInfo.accountid)
                    buy_sum += 1

                # --- 弱势股处理：连续5天下跌 ---
                elif all(diff < 0) and holdings[stock] > selltarget_num:
                    # 计算需要减少的持仓
                    sell_num = holdings[stock] - selltarget_num

                    # 执行减仓操作（负数表示卖出）
                    order_shares(stock, (-1.0)*sell_num*100, 'fix', pre_close, ContextInfo, ContextInfo.accountid)
                    sell_sum += 1

    # === 第四步：绘制监控图表 ===

    # 如果不是回测模式，绘制交易统计图表
    if not ContextInfo.do_back_test:
        # 绘制买入次数曲线
        ContextInfo.paint('buy_num', buy_sum, -1, 0)
        # 绘制卖出次数曲线
        ContextInfo.paint('sell_num', sell_sum, -1, 0)
					
def get_holdings(accountid, datatype):
    """
    获取账户持仓信息

    功能：查询指定账户的股票持仓情况并转换为字典格式

    参数：
        accountid: 账户ID字符串
        datatype: 账户类型（如"STOCK"表示股票账户）

    返回值：
        holdinglist: 持仓字典，格式为{股票代码: 持仓手数}

    数据处理：
        - 将持仓数量从股数转换为手数（除以100）
        - 股票代码格式：证券代码.交易所代码（如000001.SZ）

    用途：
        - 获取当前持仓状况
        - 计算持仓市值
        - 判断是否需要调仓

    注意：
        - 返回的是手数，不是股数
        - 仅包含有持仓的股票
        - 数据来源于交易系统的实时持仓
    """
    # 初始化持仓字典
    holdinglist = {}

    # 调用QMT API获取持仓明细数据
    resultlist = get_trade_detail_data(accountid, datatype, "POSITION")

    # 遍历持仓记录，构建持仓字典
    for obj in resultlist:
        # 构建完整的股票代码（证券代码.交易所代码）
        stock_code = obj.m_strInstrumentID + "." + obj.m_strExchangeID

        # 将持仓数量从股数转换为手数（1手=100股）
        holdinglist[stock_code] = obj.m_nVolume / 100

    return holdinglist

def get_avaliablecost(accountid, datatype):
    """
    获取账户可用资金

    功能：查询指定账户的可用资金余额

    参数：
        accountid: 账户ID字符串
        datatype: 账户类型（如"STOCK"表示股票账户）

    返回值：
        result: 可用资金金额（浮点数，单位：元）

    数据来源：
        - 从交易系统获取账户资金信息
        - 使用m_dAvailable字段（可用资金）

    用途：
        - 计算总资产规模
        - 判断是否有足够资金买入
        - 进行资产配置计算

    注意：
        - 返回的是可用资金，不是总资产
        - 不包括已冻结的资金
        - 实时数据，反映当前账户状态
    """
    # 初始化可用资金为0
    result = 0

    # 调用QMT API获取账户资金信息
    resultlist = get_trade_detail_data(accountid, datatype, "ACCOUNT")

    # 遍历账户信息（通常只有一条记录）
    for obj in resultlist:
        # 获取可用资金金额
        result = obj.m_dAvailable

    return result

