"""
简单的优化版本测试
"""

import numpy as np
import time
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def generate_test_data(num_bars=100):
    """生成测试数据"""
    np.random.seed(42)
    base_price = 100.0
    klines = []
    
    for i in range(num_bars):
        change = np.random.normal(0, 0.02)
        base_price *= (1 + change)
        
        high = base_price * (1 + abs(np.random.normal(0, 0.01)))
        low = base_price * (1 - abs(np.random.normal(0, 0.01)))
        close = base_price
        open_price = klines[-1]['close'] if klines else base_price
        volume = np.random.randint(1000, 10000)
        
        klines.append({
            'open': open_price,
            'high': max(high, open_price, close),
            'low': min(low, open_price, close),
            'close': close,
            'volume': volume
        })
    
    return klines

def test_optimized_detector():
    """测试优化版检测器"""
    print("🚀 测试优化版检测器...")
    
    try:
        # 导入优化版
        from CMF_BIAS背离检测器_优化版v1 import OptimizedCMFBIASDivergenceDetector
        
        # 创建检测器
        detector = OptimizedCMFBIASDivergenceDetector()
        print("✅ 优化版检测器创建成功")
        
        # 生成测试数据
        test_data = generate_test_data(200)
        print(f"✅ 生成测试数据: {len(test_data)}根K线")
        
        # 性能测试
        iterations = 5
        start_time = time.perf_counter()
        
        results = []
        for i in range(iterations):
            result = detector.get_comprehensive_signals_optimized(test_data)
            results.append(result)
            print(f"   第{i+1}次测试: {result.get('status', 'unknown')}")
        
        end_time = time.perf_counter()
        
        # 计算性能
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        print(f"\n📊 性能测试结果:")
        print(f"   总耗时: {total_time*1000:.2f}ms")
        print(f"   平均耗时: {avg_time*1000:.2f}ms")
        print(f"   成功率: {sum(1 for r in results if r.get('status') == 'success') / len(results) * 100:.1f}%")
        
        # 显示最后一次结果
        if results and results[-1].get('status') == 'success':
            last_result = results[-1]
            print(f"\n📈 最后一次计算结果:")
            indicators = last_result.get('indicators', {})
            conditions = last_result.get('conditions', {})
            
            print(f"   指标值:")
            for key, value in indicators.items():
                print(f"     {key}: {value:.4f}")
            
            print(f"   条件判断:")
            for key, value in conditions.items():
                print(f"     {key}: {value}")
            
            print(f"   买入信号: {last_result.get('buy_signal', False)}")
            
            # 优化信息
            opt_info = last_result.get('optimization_info', {})
            if opt_info:
                print(f"   优化版本: {opt_info.get('version', 'unknown')}")
                print(f"   优化项目: {opt_info.get('optimizations', [])}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_detector():
    """测试原版检测器（如果可用）"""
    print("\n🔄 测试原版检测器...")
    
    try:
        # 尝试导入原版
        sys.path.append(os.path.join(parent_dir, '框架'))
        from CMF_BIAS背离检测器 import CMFBIASDivergenceDetector
        
        # 创建检测器
        detector = CMFBIASDivergenceDetector()
        print("✅ 原版检测器创建成功")
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 性能测试
        iterations = 5
        start_time = time.perf_counter()
        
        results = []
        for i in range(iterations):
            result = detector.get_comprehensive_signals(test_data)
            results.append(result)
            print(f"   第{i+1}次测试: {result.get('status', 'unknown')}")
        
        end_time = time.perf_counter()
        
        # 计算性能
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        print(f"\n📊 原版性能测试结果:")
        print(f"   总耗时: {total_time*1000:.2f}ms")
        print(f"   平均耗时: {avg_time*1000:.2f}ms")
        print(f"   成功率: {sum(1 for r in results if r.get('status') == 'success') / len(results) * 100:.1f}%")
        
        return avg_time
        
    except ImportError as e:
        print(f"⚠️ 原版检测器导入失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 原版测试失败: {e}")
        return None

def main():
    """主测试函数"""
    print("=" * 60)
    print("CMF+BIAS双重背离策略优化测试")
    print("=" * 60)
    
    # 测试优化版
    opt_success = test_optimized_detector()
    
    # 测试原版（如果可用）
    orig_time = test_original_detector()
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    if opt_success:
        print("✅ 优化版测试成功")
    else:
        print("❌ 优化版测试失败")
    
    if orig_time:
        print("✅ 原版测试成功")
        print("📈 可以进行性能对比")
    else:
        print("⚠️ 原版测试不可用，无法进行性能对比")
    
    print("\n🎯 优化版本特性:")
    print("   1. 向量化ADX计算 - 消除Python循环")
    print("   2. pandas DataFrame数据处理 - 提升数据提取效率")
    print("   3. 优化的背离检测 - 减少重复计算")
    print("   4. 向量化指标计算 - 提升整体性能")

if __name__ == "__main__":
    main()
