#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试ADX数组索引错误
"""

def debug_adx_step_by_step():
    """逐步调试ADX计算"""
    try:
        print("=== 逐步调试ADX计算 ===")
        
        import numpy as np
        import talib
        
        # 创建简单测试数据
        print("📊 创建测试数据...")
        highs = np.array([10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7, 11.8, 11.9, 12.0], dtype=np.float64)
        lows = np.array([10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5], dtype=np.float64)
        closes = np.array([10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7], dtype=np.float64)
        
        print(f"   数据类型: highs={highs.dtype}, lows={lows.dtype}, closes={closes.dtype}")
        print(f"   数据长度: {len(highs)}根K线")
        
        ADX_N = 14
        ADX_M = 7
        
        print(f"   ADX参数: ADX_N={ADX_N}, ADX_M={ADX_M}")
        
        # 步骤1：计算HL
        print("\n🔍 步骤1: 计算HL...")
        hl = highs - lows
        print(f"   HL计算成功，长度: {len(hl)}, 类型: {hl.dtype}")
        print(f"   HL前3个值: {hl[:3]}")
        
        # 步骤2：计算HC和LC
        print("\n🔍 步骤2: 计算HC和LC...")
        if len(highs) < 2 or len(closes) < 2 or len(lows) < 2:
            print("   ❌ 数据长度不足")
            return False
            
        print(f"   highs[1:] 长度: {len(highs[1:])}")
        print(f"   closes[:-1] 长度: {len(closes[:-1])}")
        
        hc = np.abs(highs[1:] - closes[:-1])
        lc = np.abs(closes[:-1] - lows[1:])
        
        print(f"   HC计算成功，长度: {len(hc)}, 类型: {hc.dtype}")
        print(f"   LC计算成功，长度: {len(lc)}, 类型: {lc.dtype}")
        print(f"   HC前3个值: {hc[:3]}")
        print(f"   LC前3个值: {lc[:3]}")
        
        # 步骤3：数组对齐
        print("\n🔍 步骤3: 数组对齐...")
        print(f"   hl[0] = {hl[0]}, 类型: {type(hl[0])}")
        
        try:
            # 方法1：使用np.array包装
            hc_aligned = np.concatenate([np.array([hl[0]]), hc])
            lc_aligned = np.concatenate([np.array([hl[0]]), lc])
            print(f"   方法1成功: hc_aligned长度={len(hc_aligned)}, lc_aligned长度={len(lc_aligned)}")
        except Exception as e:
            print(f"   方法1失败: {e}")
            
            # 方法2：使用reshape
            try:
                hc_aligned = np.concatenate([hl[0:1], hc])
                lc_aligned = np.concatenate([hl[0:1], lc])
                print(f"   方法2成功: hc_aligned长度={len(hc_aligned)}, lc_aligned长度={len(lc_aligned)}")
            except Exception as e2:
                print(f"   方法2失败: {e2}")
                
                # 方法3：手动构建
                try:
                    hc_aligned = np.zeros(len(hl))
                    lc_aligned = np.zeros(len(hl))
                    hc_aligned[0] = hl[0]
                    lc_aligned[0] = hl[0]
                    hc_aligned[1:] = hc
                    lc_aligned[1:] = lc
                    print(f"   方法3成功: hc_aligned长度={len(hc_aligned)}, lc_aligned长度={len(lc_aligned)}")
                except Exception as e3:
                    print(f"   方法3失败: {e3}")
                    return False
        
        # 步骤4：计算TR
        print("\n🔍 步骤4: 计算TR...")
        tr = np.maximum(np.maximum(hl, hc_aligned), lc_aligned)
        print(f"   TR计算成功，长度: {len(tr)}, 类型: {tr.dtype}")
        print(f"   TR前3个值: {tr[:3]}")
        
        # 步骤5：计算MTR
        print("\n🔍 步骤5: 计算MTR...")
        MTR = talib.SUM(tr.astype(np.float64), timeperiod=ADX_N)
        print(f"   MTR计算成功，长度: {len(MTR)}, 类型: {MTR.dtype}")
        print(f"   MTR非零值数量: {np.count_nonzero(MTR)}")
        print(f"   MTR最后3个值: {MTR[-3:]}")
        
        # 步骤6：计算HD和LD
        print("\n🔍 步骤6: 计算HD和LD...")
        try:
            HD = np.concatenate([np.array([0.0]), highs[1:] - highs[:-1]])
            LD = np.concatenate([np.array([0.0]), lows[:-1] - lows[1:]])
            print(f"   HD计算成功，长度: {len(HD)}, 类型: {HD.dtype}")
            print(f"   LD计算成功，长度: {len(LD)}, 类型: {LD.dtype}")
        except Exception as e:
            print(f"   HD/LD计算失败: {e}")
            return False
        
        # 步骤7：计算DMP和DMM
        print("\n🔍 步骤7: 计算DMP和DMM...")
        dmp_condition = (HD > 0) & (HD > LD)
        dmp_values = np.where(dmp_condition, HD, 0.0).astype(np.float64)
        DMP = talib.SUM(dmp_values, timeperiod=ADX_N)
        
        dmm_condition = (LD > 0) & (LD > HD)
        dmm_values = np.where(dmm_condition, LD, 0.0).astype(np.float64)
        DMM = talib.SUM(dmm_values, timeperiod=ADX_N)
        
        print(f"   DMP计算成功，长度: {len(DMP)}")
        print(f"   DMM计算成功，长度: {len(DMM)}")
        
        # 步骤8：计算PDI和MDI
        print("\n🔍 步骤8: 计算PDI和MDI...")
        MTR = np.where(MTR == 0, 1e-8, MTR)
        PDI = (DMP * 100.0 / MTR).astype(np.float64)
        MDI = (DMM * 100.0 / MTR).astype(np.float64)
        
        print(f"   PDI计算成功，长度: {len(PDI)}")
        print(f"   MDI计算成功，长度: {len(MDI)}")
        
        # 步骤9：计算ADX
        print("\n🔍 步骤9: 计算ADX...")
        dx_numerator = np.abs(MDI - PDI)
        dx_denominator = MDI + PDI
        dx_denominator = np.where(dx_denominator == 0, 1e-8, dx_denominator)
        DX = (dx_numerator / dx_denominator * 100.0).astype(np.float64)
        
        ADX = talib.SMA(DX, timeperiod=ADX_M)
        ADX = np.nan_to_num(ADX, nan=0.0, posinf=0.0, neginf=0.0)
        
        print(f"   ADX计算成功，长度: {len(ADX)}")
        print(f"   ADX最后值: {ADX[-1]:.2f}")
        
        if ADX[-1] > 0:
            print("✅ ADX计算完全成功！")
        else:
            print("⚠️ ADX为0，可能需要更多数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始ADX逐步调试...")
    debug_adx_step_by_step()
