#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的ADX修复验证测试
"""

def simple_test():
    try:
        print("开始简单ADX测试...")
        
        import sys
        import os
        sys.path.append(os.path.join(os.getcwd(), '框架'))
        
        # 直接导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(ADX_N=14, ADX_M=7)
        print(f"检测器创建成功，ADX_N={detector.ADX_N}, ADX_M={detector.ADX_M}")
        
        # 创建简单测试数据
        highs = [10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7, 11.8, 11.9, 12.0]
        lows = [10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5]
        closes = [10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 11.7]
        
        print(f"测试数据: {len(highs)}根K线，价格从{closes[0]}到{closes[-1]}")
        
        # 测试ADX计算
        print("开始ADX计算...")
        adx_result = detector.calculate_ADX(highs, lows, closes)
        print(f"ADX计算完成，结果长度: {len(adx_result)}")
        print(f"ADX最后值: {adx_result[-1]:.2f}")
        
        if adx_result[-1] > 0:
            print("✅ ADX修复成功！")
        else:
            print("❌ ADX仍为0")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_test()
