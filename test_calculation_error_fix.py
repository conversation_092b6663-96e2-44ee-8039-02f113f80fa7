#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试calculation_error问题修复
"""

def test_missing_methods():
    """测试缺失方法是否已添加"""
    try:
        print("=== 测试缺失方法修复 ===")
        
        import importlib.util
        import numpy as np
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector()
        
        # 生成测试数据
        test_data_count = 50
        highs = np.array([10.5 + i * 0.1 + np.random.random() * 0.05 for i in range(test_data_count)])
        lows = np.array([9.8 + i * 0.1 - np.random.random() * 0.05 for i in range(test_data_count)])
        closes = np.array([10.2 + i * 0.1 + np.random.random() * 0.02 for i in range(test_data_count)])
        volumes = np.array([1000 + i * 50 + np.random.random() * 100 for i in range(test_data_count)])
        
        print(f"📊 测试数据: {test_data_count}个K线")
        
        # 测试calculate_BIAS方法
        print("🧪 测试calculate_BIAS方法...")
        if hasattr(detector, 'calculate_BIAS'):
            bias_result = detector.calculate_BIAS(closes)
            print(f"   ✅ calculate_BIAS存在，返回数组长度: {len(bias_result)}")
            print(f"   📊 BIAS范围: {bias_result.min():.2f}% ~ {bias_result.max():.2f}%")
        else:
            print("   ❌ calculate_BIAS方法不存在")
            return False
        
        # 测试calculate_ADX方法
        print("🧪 测试calculate_ADX方法...")
        if hasattr(detector, 'calculate_ADX'):
            adx_result = detector.calculate_ADX(highs, lows, closes)
            print(f"   ✅ calculate_ADX存在，返回数组长度: {len(adx_result)}")
            print(f"   📊 ADX范围: {adx_result.min():.2f} ~ {adx_result.max():.2f}")
        else:
            print("   ❌ calculate_ADX方法不存在")
            return False
        
        # 测试综合信号检测
        print("🧪 测试综合信号检测...")
        merged_klines = []
        for i in range(test_data_count):
            kline = {
                'open': 10.0 + i * 0.1,
                'high': highs[i],
                'low': lows[i],
                'close': closes[i],
                'volume': volumes[i]
            }
            merged_klines.append(kline)
        
        result = detector.get_comprehensive_signals(merged_klines)
        print(f"   状态: {result['status']}")
        
        if result['status'] == 'success':
            print("   ✅ 综合信号检测成功")
            indicators = result.get('indicators', {})
            print(f"   📊 BIAS值: {indicators.get('BIAS', 0):.2f}%")
            print(f"   📊 ADX值: {indicators.get('ADX', 0):.2f}")
            return True
        elif result['status'] == 'calculation_error':
            print(f"   ❌ 计算错误: {result.get('error_message', '未知错误')}")
            return False
        else:
            print(f"   ⚠️ 其他状态: {result['status']}")
            return True  # 数据不足等状态是正常的
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bias_calculation():
    """测试BIAS计算的正确性"""
    try:
        print("\n=== 测试BIAS计算正确性 ===")
        
        import importlib.util
        import numpy as np
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(BIAS_N=5)  # 使用较小的周期便于测试
        
        # 生成简单的测试数据
        closes = np.array([10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9])
        
        # 计算BIAS
        bias_result = detector.calculate_BIAS(closes)
        
        # 手动验证最后一个值
        # MA(5) of last 5 values: (10.5+10.6+10.7+10.8+10.9)/5 = 10.7
        # BIAS = (10.9 - 10.7) / 10.7 * 100 = 1.87%
        expected_bias = (10.9 - 10.7) / 10.7 * 100
        actual_bias = bias_result[-1]
        
        print(f"📊 测试数据: {closes}")
        print(f"📊 BIAS结果: {bias_result}")
        print(f"📊 预期最后值: {expected_bias:.2f}%")
        print(f"📊 实际最后值: {actual_bias:.2f}%")
        
        # 允许小的数值误差
        if abs(actual_bias - expected_bias) < 0.1:
            print("✅ BIAS计算正确")
            return True
        else:
            print("❌ BIAS计算有误")
            return False
        
    except Exception as e:
        print(f"❌ BIAS计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adx_calculation():
    """测试ADX计算的基本功能"""
    try:
        print("\n=== 测试ADX计算基本功能 ===")
        
        import importlib.util
        import numpy as np
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(ADX_N=14)  # 使用标准ADX周期
        
        # 生成趋势性测试数据
        test_count = 30
        highs = np.array([10.0 + i * 0.1 + 0.05 for i in range(test_count)])
        lows = np.array([10.0 + i * 0.1 - 0.05 for i in range(test_count)])
        closes = np.array([10.0 + i * 0.1 for i in range(test_count)])
        
        # 计算ADX
        adx_result = detector.calculate_ADX(highs, lows, closes)
        
        print(f"📊 测试数据长度: {test_count}")
        print(f"📊 ADX结果长度: {len(adx_result)}")
        print(f"📊 ADX范围: {adx_result.min():.2f} ~ {adx_result.max():.2f}")
        print(f"📊 最后5个ADX值: {adx_result[-5:]}")
        
        # 基本合理性检查
        if len(adx_result) == test_count and not np.isnan(adx_result[-1]):
            print("✅ ADX计算基本功能正常")
            return True
        else:
            print("❌ ADX计算有问题")
            return False
        
    except Exception as e:
        print(f"❌ ADX计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始calculation_error修复测试...")
    
    success_count = 0
    total_tests = 3
    
    if test_missing_methods():
        success_count += 1
    
    if test_bias_calculation():
        success_count += 1
    
    if test_adx_calculation():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！calculation_error问题已修复")
        print("\n📋 修复总结:")
        print("1. ✅ 添加了缺失的calculate_BIAS方法")
        print("2. ✅ 添加了缺失的calculate_ADX方法")
        print("3. ✅ BIAS计算公式正确：(CLOSE-MA)/MA*100")
        print("4. ✅ ADX使用talib库标准实现")
        print("5. ✅ 所有方法都有异常处理和NaN值处理")
    else:
        print("❌ 部分测试失败，需要进一步检查")
