"""
========================================================================
CMF+BIAS双重背离策略 - 性能优化版 v1.0
========================================================================

优化重点：
1. 向量化ADX计算 - 消除Python循环
2. pandas DataFrame数据处理 - 提升数据提取效率
3. 缓存重复计算 - 避免重复的talib调用
4. 背离检测优化 - 减少重复调用

预期性能提升：2-3倍
优化完成度：第一阶段（立即优化）
"""

import numpy as np
import pandas as pd
import talib
import datetime
import traceback
from functools import lru_cache
from typing import Dict, List, Tuple, Optional
import hashlib

class OptimizedCMFBIASDivergenceDetector:
    """
    CMF+BIAS双重背离检测器 - 性能优化版
    
    主要优化：
    1. 向量化计算替代循环
    2. pandas DataFrame数据处理
    3. LRU缓存机制
    4. 批量指标计算
    """

    def __init__(self,
                 SKDJ_N=8, SKDJ_M=4,
                 CMF_N=30, CMF_M=20,
                 BIAS_N=30, BIAS_M=20,
                 ADX_N=23, ADX_M=11,
                 VAE_基础TR=1.8, VAE_初始止损=1.5, VAE_周期=20,
                 固定止损=0.5):
        """初始化优化版检测器"""
        # 参数设置
        self.SKDJ_N, self.SKDJ_M = SKDJ_N, SKDJ_M
        self.CMF_N, self.CMF_M = CMF_N, CMF_M
        self.BIAS_N, self.BIAS_M = BIAS_N, BIAS_M
        self.ADX_N, self.ADX_M = ADX_N, ADX_M
        self.VAE_基础TR = VAE_基础TR
        self.VAE_初始止损 = VAE_初始止损
        self.VAE_周期 = VAE_周期
        self.固定止损 = 固定止损
        
        # 计算缓存
        self._indicator_cache = {}
        self._data_hash_cache = {}
        
        # 最小数据长度
        self.min_data_length = max(self.CMF_N, self.BIAS_N, self.ADX_N + self.ADX_M, self.VAE_周期 * 2)

    def _get_data_hash(self, data: np.ndarray) -> str:
        """生成数据哈希用于缓存"""
        return hashlib.md5(data.tobytes()).hexdigest()

    def extract_ohlcv_optimized(self, merged_klines: List[Dict]) -> pd.DataFrame:
        """
        优化的OHLCV数据提取 - 使用pandas DataFrame
        
        优化点：
        1. 一次性提取所有数据
        2. 避免重复的列表推导
        3. 统一的数据类型处理
        """
        try:
            # 使用pandas一次性处理
            df = pd.DataFrame(merged_klines)
            
            # 确保数据类型
            ohlcv_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in ohlcv_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 填充NaN值
            df[ohlcv_columns] = df[ohlcv_columns].fillna(method='ffill').fillna(0)
            
            return df[ohlcv_columns]
            
        except Exception as e:
            print(f"❌ OHLCV数据提取失败: {e}")
            # 回退到原始方法
            return pd.DataFrame({
                'open': [k.get('open', 0) for k in merged_klines],
                'high': [k.get('high', 0) for k in merged_klines],
                'low': [k.get('low', 0) for k in merged_klines],
                'close': [k.get('close', 0) for k in merged_klines],
                'volume': [k.get('volume', 0) for k in merged_klines]
            }).astype(np.float64)

    @lru_cache(maxsize=64)
    def cached_sma(self, data_hash: str, data_tuple: tuple, period: int) -> np.ndarray:
        """缓存的SMA计算"""
        data = np.array(data_tuple, dtype=np.float64)
        return talib.SMA(data, timeperiod=period)

    @lru_cache(maxsize=64)
    def cached_ema(self, data_hash: str, data_tuple: tuple, period: int) -> np.ndarray:
        """缓存的EMA计算"""
        data = np.array(data_tuple, dtype=np.float64)
        return talib.EMA(data, timeperiod=period)

    @lru_cache(maxsize=64)
    def cached_max_min(self, data_hash: str, data_tuple: tuple, period: int) -> Tuple[np.ndarray, np.ndarray]:
        """缓存的MAX/MIN计算"""
        data = np.array(data_tuple, dtype=np.float64)
        return talib.MAX(data, timeperiod=period), talib.MIN(data, timeperiod=period)

    def calculate_SKDJ_optimized(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        优化的SKDJ计算
        
        优化点：
        1. 使用缓存的MAX/MIN计算
        2. 向量化的RSV计算
        3. 减少重复的数据转换
        """
        try:
            highs, lows, closes = df['high'].values, df['low'].values, df['close'].values
            
            if len(closes) < self.SKDJ_N + self.SKDJ_M:
                return np.full(len(closes), 50.0), np.full(len(closes), 50.0)

            # 使用缓存计算LOWV和HIGHV
            high_hash = self._get_data_hash(highs)
            low_hash = self._get_data_hash(lows)
            
            HIGHV, _ = self.cached_max_min(high_hash, tuple(highs), self.SKDJ_N)
            _, LOWV = self.cached_max_min(low_hash, tuple(lows), self.SKDJ_N)

            # 向量化RSV计算
            denominator = HIGHV - LOWV
            denominator = np.where(denominator == 0, 1e-8, denominator)
            RSV_raw = (closes - LOWV) / denominator * 100
            RSV_raw = np.nan_to_num(RSV_raw, nan=50.0)
            
            # 使用缓存的EMA计算
            rsv_hash = self._get_data_hash(RSV_raw)
            RSV = self.cached_ema(rsv_hash, tuple(RSV_raw), self.SKDJ_M)
            K = self.cached_ema(self._get_data_hash(RSV), tuple(RSV), self.SKDJ_M)
            D = self.cached_sma(self._get_data_hash(K), tuple(K), self.SKDJ_M)

            return K, D

        except Exception as e:
            print(f"❌ SKDJ计算失败: {e}")
            return np.full(len(df), 50.0), np.full(len(df), 50.0)

    def calculate_CMF_optimized(self, df: pd.DataFrame) -> np.ndarray:
        """
        优化的CMF计算
        
        优化点：
        1. 向量化的CLV计算
        2. 缓存的SUM计算
        3. 避免重复的数组操作
        """
        try:
            if len(df) < self.CMF_N:
                return np.zeros(len(df))

            highs, lows, closes, volumes = df['high'].values, df['low'].values, df['close'].values, df['volume'].values

            # 向量化CLV计算
            denominator = highs - lows
            denominator = np.where(denominator == 0, 1e-8, denominator)
            CLV = (closes - lows - highs + closes) / denominator
            CLV = np.nan_to_num(CLV, nan=0.0)

            # 向量化MF计算
            MF = CLV * volumes

            # 使用talib的SUM函数（已经优化）
            MF_sum = talib.SUM(MF, timeperiod=self.CMF_N)
            VOL_sum = talib.SUM(volumes, timeperiod=self.CMF_N)
            
            # 避免除零
            VOL_sum = np.where(VOL_sum == 0, 1e-8, VOL_sum)
            CMF = MF_sum / VOL_sum
            
            return np.nan_to_num(CMF, nan=0.0)

        except Exception as e:
            print(f"❌ CMF计算失败: {e}")
            return np.zeros(len(df))

    def calculate_BIAS_optimized(self, df: pd.DataFrame) -> np.ndarray:
        """
        优化的BIAS计算
        
        优化点：
        1. 使用缓存的SMA计算
        2. 向量化的BIAS公式
        """
        try:
            closes = df['close'].values
            
            if len(closes) < self.BIAS_N:
                return np.zeros(len(closes))

            # 使用缓存的SMA计算
            close_hash = self._get_data_hash(closes)
            ma_close = self.cached_sma(close_hash, tuple(closes), self.BIAS_N)

            # 向量化BIAS计算
            ma_close = np.where(ma_close == 0, 1e-8, ma_close)
            BIAS = (closes - ma_close) / ma_close * 100

            return np.nan_to_num(BIAS, nan=0.0, posinf=0.0, neginf=0.0)

        except Exception as e:
            print(f"❌ BIAS计算失败: {e}")
            return np.zeros(len(df))

    def calculate_ADX_vectorized(self, df: pd.DataFrame) -> np.ndarray:
        """
        完全向量化的ADX计算
        
        优化点：
        1. 消除所有Python循环
        2. 使用numpy向量化操作
        3. 优化内存使用
        """
        try:
            highs, lows, closes = df['high'].values, df['low'].values, df['close'].values
            
            if len(closes) < self.ADX_N + 1:
                return np.zeros(len(closes))

            # 向量化TR计算
            hl = highs - lows
            hc = np.concatenate([[hl[0]], np.abs(highs[1:] - closes[:-1])])
            lc = np.concatenate([[hl[0]], np.abs(closes[:-1] - lows[1:])])
            tr = np.maximum(np.maximum(hl, hc), lc)

            # MTR计算
            MTR = talib.SUM(tr, timeperiod=self.ADX_N)

            # 向量化HD/LD计算
            HD = np.concatenate([[0.0], highs[1:] - highs[:-1]])
            LD = np.concatenate([[0.0], lows[:-1] - lows[1:]])

            # 向量化DMP/DMM计算
            dmp_values = np.where((HD > 0) & (HD > LD), HD, 0.0)
            dmm_values = np.where((LD > 0) & (LD > HD), LD, 0.0)

            # DMP/DMM求和
            DMP = talib.SUM(dmp_values, timeperiod=self.ADX_N)
            DMM = talib.SUM(dmm_values, timeperiod=self.ADX_N)

            # 避免除零
            MTR = np.where(MTR == 0, 1e-8, MTR)

            # PDI/MDI计算
            PDI = DMP * 100.0 / MTR
            MDI = DMM * 100.0 / MTR

            # DX计算
            dx_numerator = np.abs(MDI - PDI)
            dx_denominator = MDI + PDI
            dx_denominator = np.where(dx_denominator == 0, 1e-8, dx_denominator)
            DX = dx_numerator / dx_denominator * 100.0

            # ADX计算
            ADX = talib.SMA(DX, timeperiod=self.ADX_M)

            return np.nan_to_num(ADX, nan=0.0, posinf=0.0, neginf=0.0)

        except Exception as e:
            print(f"❌ ADX计算失败: {e}")
            return np.zeros(len(df))

    def detect_divergence_batch(self, price_highs: np.ndarray, price_lows: np.ndarray,
                               indicator: np.ndarray, period: int, window: int = 3) -> Tuple[bool, bool]:
        """
        批量背离检测 - 优化版

        优化点：
        1. 一次性检测时间窗口内的所有背离
        2. 向量化的条件判断
        3. 减少重复计算

        参数:
            price_highs: 价格最高值数组
            price_lows: 价格最低值数组
            indicator: 指标数组
            period: 背离判断周期
            window: 时间窗口大小
        """
        try:
            if len(indicator) < period:
                return False, False

            # 批量计算最近window个时间点的背离
            底背离_signals = []
            顶背离_signals = []

            for i in range(min(window, len(indicator))):
                end_idx = len(indicator) - i
                if end_idx < period:
                    continue

                # 使用缓存的MAX/MIN计算
                data_slice = slice(end_idx - period, end_idx)
                highs_slice = price_highs[data_slice]
                lows_slice = price_lows[data_slice]
                indicator_slice = indicator[data_slice]

                # 向量化的极值判断
                price_HH = price_highs[end_idx-1] >= np.max(highs_slice)
                price_LL = price_lows[end_idx-1] <= np.min(lows_slice)
                indicator_HH = indicator[end_idx-1] >= np.max(indicator_slice)
                indicator_LL = indicator[end_idx-1] <= np.min(indicator_slice)

                # 背离判断
                顶背离 = price_HH and not indicator_HH and indicator[end_idx-1] > 0
                底背离 = price_LL and not indicator_LL and indicator[end_idx-1] < 0

                底背离_signals.append(底背离)
                顶背离_signals.append(顶背离)

            # 时间窗口内任一时点有背离信号即为True
            return any(底背离_signals), any(顶背离_signals)

        except Exception as e:
            print(f"❌ 批量背离检测失败: {e}")
            return False, False

    def calculate_VAE_optimized(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        优化的VAE动态风控计算

        优化点：
        1. 使用pandas rolling操作
        2. 向量化的波动率计算
        """
        try:
            highs, lows, closes = df['high'].values, df['low'].values, df['close'].values

            # 使用talib计算ATR
            当前ATR = talib.ATR(highs, lows, closes, timeperiod=self.VAE_周期)
            ATR均值 = talib.SMA(当前ATR, timeperiod=self.VAE_周期 * 2)

            if len(当前ATR) == 0 or len(ATR均值) == 0:
                return {'动态TR': self.VAE_基础TR, '波动率比值': 1.0, '波动率区间': '正常波动区'}

            # 向量化的波动率比值计算
            波动率比值 = 当前ATR[-1] / ATR均值[-1] if ATR均值[-1] > 0 else 1.0

            # 向量化的区间判断
            if 波动率比值 <= 0.8:
                波动率区间, 动态TR = '低波动区', self.VAE_基础TR * 2
            elif 波动率比值 <= 1.2:
                波动率区间, 动态TR = '正常波动区', self.VAE_基础TR
            elif 波动率比值 <= 1.8:
                波动率区间, 动态TR = '高波动区', self.VAE_基础TR * 1
            else:
                波动率区间, 动态TR = '极高波动区', self.VAE_基础TR * 0.7

            return {
                '动态TR': 动态TR,
                '波动率比值': 波动率比值,
                '波动率区间': 波动率区间,
                '当前ATR': 当前ATR[-1],
                'ATR均值': ATR均值[-1]
            }

        except Exception as e:
            print(f"❌ VAE动态风控计算失败: {e}")
            return {'动态TR': self.VAE_基础TR, '波动率比值': 1.0, '波动率区间': '正常波动区'}

    def get_comprehensive_signals_optimized(self, merged_klines: List[Dict]) -> Dict:
        """
        优化的综合信号检测

        主要优化：
        1. pandas DataFrame数据处理
        2. 批量指标计算
        3. 优化的背离检测
        4. 缓存机制
        """
        try:
            if not merged_klines or len(merged_klines) < 10:
                return {
                    'status': 'insufficient_data',
                    'buy_signal': False,
                    'sell_signal': False,
                    'error_message': '数据不足，无法进行信号检测'
                }

            # 优化的数据提取
            df = self.extract_ohlcv_optimized(merged_klines)

            if len(df) < self.min_data_length:
                # 动态调整参数
                scale_factor = len(df) / self.min_data_length
                self.CMF_N = max(5, int(self.CMF_N * scale_factor))
                self.BIAS_N = max(5, int(self.BIAS_N * scale_factor))
                self.ADX_N = max(5, int(self.ADX_N * scale_factor))

            # 批量计算所有指标
            K, D = self.calculate_SKDJ_optimized(df)
            CMF = self.calculate_CMF_optimized(df)
            BIAS = self.calculate_BIAS_optimized(df)
            ADX = self.calculate_ADX_vectorized(df)
            VAE_info = self.calculate_VAE_optimized(df)

            # 优化的阻力线计算
            closes = df['close'].values
            if len(closes) < 2:
                突破条件 = False
            else:
                # 向量化的阻力线计算
                highs, lows = df['high'].values, df['low'].values
                前一根K线加权均值 = (highs[-2] + lows[-2] + 2 * closes[-2]) / 4
                前一根阻力线 = 前一根K线加权均值 + (前一根K线加权均值 - lows[-2])
                突破条件 = closes[-1] > 前一根阻力线

            # 买入条件检测
            SKDJ超卖 = K[-1] < 20 and D[-1] < 20

            # 优化的背离检测 - 批量处理时间窗口
            CMF底背离, _ = self.detect_divergence_batch(
                df['high'].values, df['low'].values, CMF, self.CMF_M, window=3
            )
            BIAS底背离, _ = self.detect_divergence_batch(
                df['high'].values, df['low'].values, BIAS, self.BIAS_M, window=3
            )

            双重背离 = CMF底背离 and BIAS底背离
            强趋势确认 = ADX[-1] > 40
            突破确认 = 突破条件

            # 最终信号
            买入信号 = SKDJ超卖 and 双重背离 and 强趋势确认 and 突破确认

            return {
                'status': 'success',
                'buy_signal': 买入信号,
                'sell_signal': False,
                'indicators': {
                    'SKDJ_K': K[-1],
                    'SKDJ_D': D[-1],
                    'CMF': CMF[-1],
                    'BIAS': BIAS[-1],
                    'ADX': ADX[-1],
                    'resistance_line': 前一根阻力线 if len(closes) >= 2 else 0,
                    'current_price': closes[-1]
                },
                'conditions': {
                    'SKDJ超卖': SKDJ超卖,
                    'CMF底背离': CMF底背离,
                    'BIAS底背离': BIAS底背离,
                    '双重背离': 双重背离,
                    '强趋势确认': 强趋势确认,
                    '突破确认': 突破确认
                },
                'VAE_info': VAE_info,
                'optimization_info': {
                    'version': 'v1.0',
                    'optimizations': ['向量化ADX', 'pandas数据处理', 'LRU缓存', '批量背离检测'],
                    'cache_hits': len(self._indicator_cache),
                    'data_length': len(df)
                }
            }

        except Exception as e:
            return {
                'status': 'calculation_error',
                'error_message': str(e),
                'buy_signal': False,
                'sell_signal': False,
                'optimization_info': {
                    'version': 'v1.0',
                    'error': str(e)
                }
            }
