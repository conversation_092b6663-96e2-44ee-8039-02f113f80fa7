"""
CMF+BIAS双重背离策略终极性能测试 v3.0
========================================================================

测试目标：
1. 对比所有版本的性能差异（原版、v1.0、v2.0、v3.0）
2. 验证第三阶段优化效果
3. 测试增量计算效率
4. 分析内存使用优化
5. 验证talib-free实现的正确性

第三阶段优化验证：
- 纯pandas指标计算性能
- 增量计算引擎效率
- 内存使用优化效果
- 实盘场景模拟测试
"""

import time
import numpy as np
import pandas as pd
import tracemalloc
from typing import Dict, List
import sys
import os
import gc

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

class UltimatePerformanceTestSuite:
    """终极性能测试套件"""
    
    def __init__(self):
        self.test_results = {}
        self.detectors = {}
        self.memory_profiles = {}
        
    def setup_all_detectors(self):
        """初始化所有版本的检测器"""
        print("🔧 初始化所有版本检测器...")
        
        # v1.0优化版
        try:
            from CMF_BIAS背离检测器_优化版v1 import OptimizedCMFBIASDivergenceDetector
            self.detectors['v1.0'] = OptimizedCMFBIASDivergenceDetector()
            print("✅ v1.0优化版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ v1.0优化版导入失败: {e}")
        
        # v2.0优化版
        try:
            from CMF_BIAS背离检测器_优化版v2 import AdvancedCMFBIASDivergenceDetector
            self.detectors['v2.0'] = AdvancedCMFBIASDivergenceDetector()
            print("✅ v2.0优化版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ v2.0优化版导入失败: {e}")
        
        # v3.0终极版
        try:
            from CMF_BIAS背离检测器_优化版v3 import UltimateCMFBIASDivergenceDetector
            self.detectors['v3.0'] = UltimateCMFBIASDivergenceDetector(enable_incremental=True)
            self.detectors['v3.0_direct'] = UltimateCMFBIASDivergenceDetector(enable_incremental=False)
            print("✅ v3.0终极版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ v3.0终极版导入失败: {e}")
        
        # 原版
        try:
            sys.path.append(os.path.join(parent_dir, '框架'))
            from CMF_BIAS背离检测器 import CMFBIASDivergenceDetector
            self.detectors['原版'] = CMFBIASDivergenceDetector()
            print("✅ 原版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ 原版导入失败: {e}")
        
        print(f"📊 成功初始化 {len(self.detectors)} 个检测器版本")
        return len(self.detectors) > 0
        
    def generate_realistic_test_data(self, num_bars: int, volatility: float = 0.02) -> List[Dict]:
        """生成更真实的测试数据"""
        np.random.seed(42)  # 固定随机种子
        
        base_price = 100.0
        klines = []
        
        for i in range(num_bars):
            # 模拟更真实的价格波动
            trend = 0.0001 * i  # 轻微上升趋势
            noise = np.random.normal(0, volatility)
            change = trend + noise
            
            base_price *= (1 + change)
            
            # 生成OHLC数据
            daily_volatility = abs(np.random.normal(0, 0.01))
            high = base_price * (1 + daily_volatility)
            low = base_price * (1 - daily_volatility)
            close = base_price
            open_price = klines[-1]['close'] if klines else base_price
            
            # 模拟成交量
            volume = int(np.random.lognormal(8, 0.5))  # 对数正态分布
            
            klines.append({
                'open': float(open_price),
                'high': float(max(high, open_price, close)),
                'low': float(min(low, open_price, close)),
                'close': float(close),
                'volume': volume,
                'timestamp': f"2024-01-{(i//24)+1:02d} {(i%24):02d}:00:00"
            })
            
        return klines
    
    def measure_memory_usage(self, detector, version: str, test_data: List[Dict]) -> Dict:
        """测量内存使用情况"""
        
        # 强制垃圾回收
        gc.collect()
        
        # 开始内存监控
        tracemalloc.start()
        
        try:
            # 执行计算
            if version == 'v1.0':
                result = detector.get_comprehensive_signals_optimized(test_data)
            elif version == 'v2.0':
                result = detector.get_comprehensive_signals_v2(test_data)
            elif version.startswith('v3.0'):
                result = detector.get_ultimate_signals_v3(test_data)
            else:
                result = detector.get_comprehensive_signals(test_data)
            
            # 获取内存使用情况
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            return {
                'memory_current': current / 1024 / 1024,  # MB
                'memory_peak': peak / 1024 / 1024,  # MB
                'success': result.get('status') == 'success'
            }
            
        except Exception as e:
            tracemalloc.stop()
            return {
                'memory_current': 0,
                'memory_peak': 0,
                'success': False,
                'error': str(e)
            }
    
    def test_incremental_calculation_efficiency(self):
        """测试增量计算效率"""
        print("\n🔄 测试增量计算效率...")
        print("-" * 50)
        
        if 'v3.0' not in self.detectors:
            print("⚠️ v3.0版本不可用，跳过增量计算测试")
            return
        
        detector = self.detectors['v3.0']
        
        # 生成基础数据
        base_data = self.generate_realistic_test_data(200)
        
        # 第一次计算（完整计算）
        start_time = time.perf_counter()
        result1 = detector.get_ultimate_signals_v3(base_data)
        first_calc_time = time.perf_counter() - start_time
        
        print(f"首次完整计算: {first_calc_time*1000:.2f}ms")
        
        # 模拟实盘增量更新
        incremental_times = []
        
        for i in range(10):
            # 添加新的K线数据
            new_kline = {
                'open': base_data[-1]['close'],
                'high': base_data[-1]['close'] * 1.01,
                'low': base_data[-1]['close'] * 0.99,
                'close': base_data[-1]['close'] * (1 + np.random.normal(0, 0.01)),
                'volume': int(np.random.lognormal(8, 0.5)),
                'timestamp': f"2024-01-01 {10+i:02d}:00:00"
            }
            
            updated_data = base_data + [new_kline]
            
            # 增量计算
            start_time = time.perf_counter()
            result = detector.get_ultimate_signals_v3(updated_data)
            incremental_time = time.perf_counter() - start_time
            incremental_times.append(incremental_time)
            
            strategy = result.get('performance_info', {}).get('calculation_strategy', 'unknown')
            print(f"第{i+1}次增量更新: {incremental_time*1000:.2f}ms (策略: {strategy})")
        
        avg_incremental_time = np.mean(incremental_times)
        speedup = first_calc_time / avg_incremental_time if avg_incremental_time > 0 else 0
        
        print(f"\n📈 增量计算效率分析:")
        print(f"  平均增量计算时间: {avg_incremental_time*1000:.2f}ms")
        print(f"  相对于完整计算提升: {speedup:.2f}x")
        
        return {
            'first_calc_time': first_calc_time,
            'avg_incremental_time': avg_incremental_time,
            'speedup': speedup,
            'incremental_times': incremental_times
        }
    
    def run_comprehensive_performance_test(self):
        """运行全面性能测试"""
        print("🚀 开始终极性能测试...")
        print("=" * 80)
        
        if not self.setup_all_detectors():
            print("❌ 无法初始化检测器，测试终止")
            return
        
        # 测试场景
        test_scenarios = [
            {'name': '小数据量', 'bars': 100, 'iterations': 10},
            {'name': '中数据量', 'bars': 300, 'iterations': 6},
            {'name': '大数据量', 'bars': 500, 'iterations': 4},
            {'name': '超大数据量', 'bars': 1000, 'iterations': 2}
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 测试场景: {scenario['name']} ({scenario['bars']}根K线)")
            print("-" * 70)
            
            # 生成测试数据
            test_data = self.generate_realistic_test_data(scenario['bars'])
            
            # 测试所有版本
            scenario_results = {}
            
            for version, detector in self.detectors.items():
                print(f"🔄 测试 {version}...")
                
                # 性能测试
                times = []
                success_count = 0
                
                for i in range(scenario['iterations']):
                    try:
                        start_time = time.perf_counter()
                        
                        if version == 'v1.0':
                            result = detector.get_comprehensive_signals_optimized(test_data)
                        elif version == 'v2.0':
                            result = detector.get_comprehensive_signals_v2(test_data)
                        elif version.startswith('v3.0'):
                            result = detector.get_ultimate_signals_v3(test_data)
                        else:
                            result = detector.get_comprehensive_signals(test_data)
                        
                        calc_time = time.perf_counter() - start_time
                        times.append(calc_time)
                        
                        if result.get('status') == 'success':
                            success_count += 1
                            
                    except Exception as e:
                        print(f"   ❌ 第{i+1}次执行失败: {e}")
                
                # 内存测试
                memory_info = self.measure_memory_usage(detector, version, test_data)
                
                # 统计结果
                if times:
                    avg_time = np.mean(times)
                    min_time = np.min(times)
                    max_time = np.max(times)
                    std_time = np.std(times)
                else:
                    avg_time = min_time = max_time = std_time = 0
                
                success_rate = success_count / scenario['iterations']
                
                scenario_results[version] = {
                    'avg_time': avg_time,
                    'min_time': min_time,
                    'max_time': max_time,
                    'std_time': std_time,
                    'success_rate': success_rate,
                    'memory_peak': memory_info['memory_peak'],
                    'memory_current': memory_info['memory_current'],
                    'times': times
                }
                
                print(f"   平均耗时: {avg_time*1000:.2f}ms ± {std_time*1000:.2f}ms")
                print(f"   内存峰值: {memory_info['memory_peak']:.2f}MB")
                print(f"   成功率: {success_rate*100:.1f}%")
            
            # 性能对比分析
            if len(scenario_results) > 1:
                print(f"\n📈 性能对比分析:")
                
                # 找到最快的版本
                fastest_version = min(scenario_results.keys(), 
                                    key=lambda v: scenario_results[v]['avg_time'])
                fastest_time = scenario_results[fastest_version]['avg_time']
                
                # 计算相对性能
                for version, result in scenario_results.items():
                    if result['avg_time'] > 0:
                        speedup = fastest_time / result['avg_time']
                        if version == fastest_version:
                            print(f"   {version}: 基准 (最快)")
                        else:
                            print(f"   {version}: {speedup:.2f}x 相对于最快版本")
                    else:
                        print(f"   {version}: 计算失败")
                
                # 内存使用对比
                print(f"\n💾 内存使用对比:")
                for version, result in scenario_results.items():
                    print(f"   {version}: {result['memory_peak']:.2f}MB")
            
            # 保存测试结果
            self.test_results[scenario['name']] = scenario_results
        
        # 增量计算效率测试
        incremental_results = self.test_incremental_calculation_efficiency()
        
        # 生成最终报告
        self.generate_ultimate_report(incremental_results)
    
    def generate_ultimate_report(self, incremental_results=None):
        """生成终极测试报告"""
        print("\n" + "=" * 80)
        print("📋 终极性能测试报告")
        print("=" * 80)
        
        if not self.test_results:
            print("\n⚠️ 没有测试结果可报告")
            return
        
        # 统计各版本的平均性能
        version_stats = {}
        
        for scenario_name, scenario_results in self.test_results.items():
            for version, result in scenario_results.items():
                if version not in version_stats:
                    version_stats[version] = {
                        'avg_times': [],
                        'memory_peaks': [],
                        'success_rates': []
                    }
                
                version_stats[version]['avg_times'].append(result['avg_time'])
                version_stats[version]['memory_peaks'].append(result['memory_peak'])
                version_stats[version]['success_rates'].append(result['success_rate'])
        
        # 计算总体统计
        print(f"\n🎯 各版本总体性能:")
        performance_summary = {}
        
        for version, stats in version_stats.items():
            avg_time = np.mean(stats['avg_times'])
            avg_memory = np.mean(stats['memory_peaks'])
            avg_success = np.mean(stats['success_rates'])
            
            performance_summary[version] = {
                'avg_time': avg_time,
                'avg_memory': avg_memory,
                'avg_success': avg_success
            }
            
            print(f"\n{version}:")
            print(f"  平均耗时: {avg_time*1000:.2f}ms")
            print(f"  平均内存: {avg_memory:.2f}MB")
            print(f"  平均成功率: {avg_success*100:.1f}%")
        
        # 性能提升分析
        if '原版' in performance_summary:
            baseline_time = performance_summary['原版']['avg_time']
            baseline_memory = performance_summary['原版']['avg_memory']
            
            print(f"\n🚀 相对于原版的优化效果:")
            for version, stats in performance_summary.items():
                if version == '原版':
                    continue
                
                time_speedup = baseline_time / stats['avg_time'] if stats['avg_time'] > 0 else 0
                memory_reduction = (baseline_memory - stats['avg_memory']) / baseline_memory * 100
                
                print(f"  {version}:")
                print(f"    性能提升: {time_speedup:.2f}x")
                print(f"    内存优化: {memory_reduction:.1f}%")
        
        # 第三阶段优化效果
        if 'v3.0' in performance_summary and 'v2.0' in performance_summary:
            v2_time = performance_summary['v2.0']['avg_time']
            v3_time = performance_summary['v3.0']['avg_time']
            v3_speedup = v2_time / v3_time if v3_time > 0 else 0
            
            print(f"\n🎉 第三阶段优化效果:")
            print(f"  v3.0 vs v2.0: {v3_speedup:.2f}x 性能提升")
            
            if incremental_results:
                print(f"  增量计算效率: {incremental_results['speedup']:.2f}x")
        
        # 技术特性总结
        print(f"\n💡 技术特性总结:")
        print(f"  ✅ v1.0: 向量化计算 + pandas数据处理")
        print(f"  ✅ v2.0: 统一指标引擎 + 智能缓存")
        print(f"  ✅ v3.0: 纯pandas指标 + 增量计算 + 内存优化")
        
        print(f"\n🎉 测试完成！共测试了 {len(version_stats)} 个版本，{len(self.test_results)} 个场景")

if __name__ == "__main__":
    # 运行终极性能测试
    test_suite = UltimatePerformanceTestSuite()
    test_suite.run_comprehensive_performance_test()
