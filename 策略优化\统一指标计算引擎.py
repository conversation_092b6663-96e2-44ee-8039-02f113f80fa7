"""
统一指标计算引擎 - 第二阶段优化
========================================================================

优化目标：
1. 批量计算所有技术指标
2. 智能缓存系统
3. 统一的数据流水线
4. 可配置的指标组合

预期性能提升：3-5倍（相比第一阶段）
"""

import numpy as np
import pandas as pd
import talib
from functools import lru_cache
from typing import Dict, List, Tuple, Optional, Any
import hashlib
import time
from dataclasses import dataclass
from enum import Enum

class IndicatorType(Enum):
    """指标类型枚举"""
    TREND = "trend"          # 趋势指标
    MOMENTUM = "momentum"    # 动量指标
    VOLUME = "volume"        # 成交量指标
    VOLATILITY = "volatility" # 波动率指标

@dataclass
class IndicatorConfig:
    """指标配置"""
    name: str
    type: IndicatorType
    params: Dict[str, Any]
    dependencies: List[str] = None
    cache_enabled: bool = True

class UnifiedIndicatorEngine:
    """
    统一指标计算引擎
    
    特性：
    1. 批量计算多个指标
    2. 智能依赖管理
    3. 多级缓存系统
    4. 向量化操作
    5. 增量计算支持
    """
    
    def __init__(self, cache_size: int = 256):
        """初始化计算引擎"""
        self.cache_size = cache_size
        self.indicator_cache = {}
        self.data_cache = {}
        self.computation_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_computations': 0,
            'total_time': 0.0
        }
        
        # 预定义指标配置
        self.indicator_configs = self._setup_indicator_configs()
        
    def _setup_indicator_configs(self) -> Dict[str, IndicatorConfig]:
        """设置指标配置"""
        configs = {}
        
        # 基础价格指标
        configs['SMA'] = IndicatorConfig(
            name='SMA', type=IndicatorType.TREND,
            params={'periods': [5, 10, 20, 30, 60]}
        )
        
        configs['EMA'] = IndicatorConfig(
            name='EMA', type=IndicatorType.TREND,
            params={'periods': [4, 8, 12, 26]}
        )
        
        configs['MAX'] = IndicatorConfig(
            name='MAX', type=IndicatorType.TREND,
            params={'periods': [8, 20, 30]}
        )
        
        configs['MIN'] = IndicatorConfig(
            name='MIN', type=IndicatorType.TREND,
            params={'periods': [8, 20, 30]}
        )
        
        # 复合指标
        configs['SKDJ'] = IndicatorConfig(
            name='SKDJ', type=IndicatorType.MOMENTUM,
            params={'N': 8, 'M': 4},
            dependencies=['MAX', 'MIN', 'EMA', 'SMA']
        )
        
        configs['CMF'] = IndicatorConfig(
            name='CMF', type=IndicatorType.VOLUME,
            params={'N': 30},
            dependencies=['SUM']
        )
        
        configs['BIAS'] = IndicatorConfig(
            name='BIAS', type=IndicatorType.MOMENTUM,
            params={'N': 30},
            dependencies=['SMA']
        )
        
        configs['ADX'] = IndicatorConfig(
            name='ADX', type=IndicatorType.TREND,
            params={'N': 23, 'M': 11},
            dependencies=['SUM', 'SMA']
        )
        
        configs['ATR'] = IndicatorConfig(
            name='ATR', type=IndicatorType.VOLATILITY,
            params={'period': 20}
        )
        
        return configs
    
    def _get_data_hash(self, data: np.ndarray) -> str:
        """生成数据哈希"""
        return hashlib.md5(data.tobytes()).hexdigest()[:16]
    
    def _get_cache_key(self, indicator_name: str, data_hash: str, params: Dict) -> str:
        """生成缓存键"""
        param_str = "_".join([f"{k}_{v}" for k, v in sorted(params.items())])
        return f"{indicator_name}_{data_hash}_{param_str}"
    
    @lru_cache(maxsize=512)
    def _cached_talib_function(self, func_name: str, data_tuple: tuple, **kwargs) -> np.ndarray:
        """缓存的talib函数调用"""
        data = np.array(data_tuple, dtype=np.float64)
        func = getattr(talib, func_name)
        return func(data, **kwargs)
    
    def batch_calculate_basic_indicators(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """
        批量计算基础指标
        
        优化点：
        1. 一次性计算多个周期的相同指标
        2. 复用中间计算结果
        3. 向量化操作
        """
        start_time = time.perf_counter()
        results = {}
        
        # 提取基础数据
        ohlcv = {
            'open': df['open'].values,
            'high': df['high'].values,
            'low': df['low'].values,
            'close': df['close'].values,
            'volume': df['volume'].values
        }
        
        # 生成数据哈希
        data_hashes = {
            key: self._get_data_hash(data) 
            for key, data in ohlcv.items()
        }
        
        # 批量计算SMA（多个周期）
        sma_periods = self.indicator_configs['SMA'].params['periods']
        results['SMA'] = {}
        
        for period in sma_periods:
            cache_key = self._get_cache_key('SMA', data_hashes['close'], {'period': period})
            
            if cache_key in self.indicator_cache:
                results['SMA'][period] = self.indicator_cache[cache_key]
                self.computation_stats['cache_hits'] += 1
            else:
                sma_result = self._cached_talib_function(
                    'SMA', tuple(ohlcv['close']), timeperiod=period
                )
                results['SMA'][period] = sma_result
                self.indicator_cache[cache_key] = sma_result
                self.computation_stats['cache_misses'] += 1
        
        # 批量计算EMA
        ema_periods = self.indicator_configs['EMA'].params['periods']
        results['EMA'] = {}
        
        for period in ema_periods:
            cache_key = self._get_cache_key('EMA', data_hashes['close'], {'period': period})
            
            if cache_key in self.indicator_cache:
                results['EMA'][period] = self.indicator_cache[cache_key]
                self.computation_stats['cache_hits'] += 1
            else:
                ema_result = self._cached_talib_function(
                    'EMA', tuple(ohlcv['close']), timeperiod=period
                )
                results['EMA'][period] = ema_result
                self.indicator_cache[cache_key] = ema_result
                self.computation_stats['cache_misses'] += 1
        
        # 批量计算MAX/MIN
        max_min_periods = self.indicator_configs['MAX'].params['periods']
        results['MAX'] = {}
        results['MIN'] = {}
        
        for period in max_min_periods:
            # MAX计算
            cache_key_max = self._get_cache_key('MAX', data_hashes['high'], {'period': period})
            if cache_key_max in self.indicator_cache:
                results['MAX'][period] = self.indicator_cache[cache_key_max]
                self.computation_stats['cache_hits'] += 1
            else:
                max_result = self._cached_talib_function(
                    'MAX', tuple(ohlcv['high']), timeperiod=period
                )
                results['MAX'][period] = max_result
                self.indicator_cache[cache_key_max] = max_result
                self.computation_stats['cache_misses'] += 1
            
            # MIN计算
            cache_key_min = self._get_cache_key('MIN', data_hashes['low'], {'period': period})
            if cache_key_min in self.indicator_cache:
                results['MIN'][period] = self.indicator_cache[cache_key_min]
                self.computation_stats['cache_hits'] += 1
            else:
                min_result = self._cached_talib_function(
                    'MIN', tuple(ohlcv['low']), timeperiod=period
                )
                results['MIN'][period] = min_result
                self.indicator_cache[cache_key_min] = min_result
                self.computation_stats['cache_misses'] += 1
        
        # 批量计算SUM（为CMF等指标准备）
        sum_periods = [20, 30, 40]  # 常用周期
        results['SUM'] = {}
        
        for period in sum_periods:
            # Volume SUM
            cache_key = self._get_cache_key('SUM_VOL', data_hashes['volume'], {'period': period})
            if cache_key in self.indicator_cache:
                results['SUM'][f'volume_{period}'] = self.indicator_cache[cache_key]
                self.computation_stats['cache_hits'] += 1
            else:
                sum_result = self._cached_talib_function(
                    'SUM', tuple(ohlcv['volume']), timeperiod=period
                )
                results['SUM'][f'volume_{period}'] = sum_result
                self.indicator_cache[cache_key] = sum_result
                self.computation_stats['cache_misses'] += 1
        
        # 更新统计
        self.computation_stats['total_computations'] += 1
        self.computation_stats['total_time'] += time.perf_counter() - start_time
        
        return results
    
    def calculate_composite_indicators(self, df: pd.DataFrame, basic_results: Dict) -> Dict[str, np.ndarray]:
        """
        计算复合指标
        
        优化点：
        1. 复用基础指标计算结果
        2. 向量化复合计算
        3. 智能依赖管理
        """
        results = {}
        
        # 提取基础数据
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        volumes = df['volume'].values
        
        # SKDJ计算（复用MAX/MIN结果）
        skdj_config = self.indicator_configs['SKDJ']
        N, M = skdj_config.params['N'], skdj_config.params['M']
        
        if N in basic_results['MAX'] and N in basic_results['MIN']:
            HIGHV = basic_results['MAX'][N]
            LOWV = basic_results['MIN'][N]
            
            # 向量化RSV计算
            denominator = HIGHV - LOWV
            denominator = np.where(denominator == 0, 1e-8, denominator)
            RSV_raw = (closes - LOWV) / denominator * 100
            RSV_raw = np.nan_to_num(RSV_raw, nan=50.0)
            
            # 复用EMA结果或计算
            if M in basic_results['EMA']:
                # 这里需要对RSV应用EMA，但basic_results中的EMA是对close的
                # 所以还是需要计算
                RSV = talib.EMA(RSV_raw, timeperiod=M)
                K = talib.EMA(RSV, timeperiod=M)
            else:
                RSV = talib.EMA(RSV_raw, timeperiod=M)
                K = talib.EMA(RSV, timeperiod=M)
            
            # 复用SMA结果或计算
            if M in basic_results['SMA']:
                # 同样需要对K应用SMA
                D = talib.SMA(K, timeperiod=M)
            else:
                D = talib.SMA(K, timeperiod=M)
            
            results['SKDJ'] = {'K': K, 'D': D}
        
        # CMF计算（复用SUM结果）
        cmf_config = self.indicator_configs['CMF']
        cmf_n = cmf_config.params['N']
        
        # 向量化CLV计算
        denominator = highs - lows
        denominator = np.where(denominator == 0, 1e-8, denominator)
        CLV = (closes - lows - highs + closes) / denominator
        CLV = np.nan_to_num(CLV, nan=0.0)
        
        # MF计算
        MF = CLV * volumes
        
        # 使用SUM计算
        MF_sum = talib.SUM(MF, timeperiod=cmf_n)
        
        # 复用volume sum结果
        vol_sum_key = f'volume_{cmf_n}'
        if vol_sum_key in basic_results['SUM']:
            VOL_sum = basic_results['SUM'][vol_sum_key]
        else:
            VOL_sum = talib.SUM(volumes, timeperiod=cmf_n)
        
        VOL_sum = np.where(VOL_sum == 0, 1e-8, VOL_sum)
        CMF = MF_sum / VOL_sum
        results['CMF'] = np.nan_to_num(CMF, nan=0.0)
        
        # BIAS计算（复用SMA结果）
        bias_config = self.indicator_configs['BIAS']
        bias_n = bias_config.params['N']
        
        if bias_n in basic_results['SMA']:
            ma_close = basic_results['SMA'][bias_n]
            ma_close = np.where(ma_close == 0, 1e-8, ma_close)
            BIAS = (closes - ma_close) / ma_close * 100
            results['BIAS'] = np.nan_to_num(BIAS, nan=0.0, posinf=0.0, neginf=0.0)
        
        return results

    def calculate_ADX_optimized(self, df: pd.DataFrame) -> np.ndarray:
        """
        优化的ADX计算

        优化点：
        1. 完全向量化
        2. 复用SUM和SMA计算
        3. 内存优化
        """
        highs, lows, closes = df['high'].values, df['low'].values, df['close'].values
        adx_config = self.indicator_configs['ADX']
        N, M = adx_config.params['N'], adx_config.params['M']

        if len(closes) < N + 1:
            return np.zeros(len(closes))

        # 向量化TR计算
        hl = highs - lows
        hc = np.concatenate([[hl[0]], np.abs(highs[1:] - closes[:-1])])
        lc = np.concatenate([[hl[0]], np.abs(closes[:-1] - lows[1:])])
        tr = np.maximum(np.maximum(hl, hc), lc)

        # MTR计算
        MTR = talib.SUM(tr, timeperiod=N)

        # 向量化HD/LD计算
        HD = np.concatenate([[0.0], highs[1:] - highs[:-1]])
        LD = np.concatenate([[0.0], lows[:-1] - lows[1:]])

        # 向量化DMP/DMM计算
        dmp_values = np.where((HD > 0) & (HD > LD), HD, 0.0)
        dmm_values = np.where((LD > 0) & (LD > HD), LD, 0.0)

        # DMP/DMM求和
        DMP = talib.SUM(dmp_values, timeperiod=N)
        DMM = talib.SUM(dmm_values, timeperiod=N)

        # 避免除零
        MTR = np.where(MTR == 0, 1e-8, MTR)

        # PDI/MDI计算
        PDI = DMP * 100.0 / MTR
        MDI = DMM * 100.0 / MTR

        # DX计算
        dx_numerator = np.abs(MDI - PDI)
        dx_denominator = MDI + PDI
        dx_denominator = np.where(dx_denominator == 0, 1e-8, dx_denominator)
        DX = dx_numerator / dx_denominator * 100.0

        # ADX计算
        ADX = talib.SMA(DX, timeperiod=M)

        return np.nan_to_num(ADX, nan=0.0, posinf=0.0, neginf=0.0)
