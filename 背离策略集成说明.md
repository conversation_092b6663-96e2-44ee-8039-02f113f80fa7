# CMF+BIAS双重背离策略完全替换6sk线框架 - 实施说明

## 📋 策略替换完成状态

✅ **已完成的策略替换工作**：

1. **策略核心逻辑完全替换** - 从支撑阻力线突破策略改为CMF+BIAS双重背离策略
2. **多重背离检测器** - 完整的CMF资金流背离 + BIAS乖离率背离检测
3. **init函数重写** - 替换为背离策略专用参数
4. **calculate_technical_indicators函数重写** - 专注于CMF、BIAS、SKDJ、ADX计算
5. **check_entry_conditions函数重写** - 基于双重背离+超卖+强趋势+突破确认开仓
6. **check_exit_conditions函数重写** - 基于VAE动态止盈+固定止损平仓
7. **保留K线合成和下单逻辑** - 完全兼容原有框架

## 🔄 策略核心变化

### 原策略 vs 新背离策略对比

| 方面 | 原6sk线策略 | 新CMF+BIAS背离策略 |
|------|------------|-------------------|
| **策略类型** | 趋势突破策略 | 多重背离确认策略 |
| **开仓信号** | 价格突破阻力线 | 双重背离+超卖+强趋势+突破 |
| **平仓信号** | ATR止盈 + 反向突破止损 | VAE动态止盈 + 固定止损 |
| **技术指标** | 支撑阻力线 + ATR + 成交量 | CMF + BIAS + SKDJ + ADX + ATR |
| **风险控制** | ATR倍数止盈 | VAE自适应止盈 + 0.5%固定止损 |

## 🚀 使用方法

### 1. 基本配置

CMF+BIAS双重背离策略已经完全替换原有策略，主要参数：

```python
# SKDJ超卖指标参数
C.SKDJ_N = 8                   # SKDJ计算周期
C.SKDJ_M = 4                   # SKDJ平滑周期

# CMF资金流背离参数
C.CMF_N = 30                   # CMF计算周期
C.CMF_M = 20                   # CMF背离判断周期

# BIAS乖离率背离参数
C.BIAS_N = 30                  # BIAS计算周期
C.BIAS_M = 20                  # BIAS背离判断周期

# ADX趋势强度参数
C.ADX_N = 23                   # ADX计算周期
C.ADX_M = 11                   # ADX平滑周期

# VAE动态风控参数
C.VAE_基础TR = 1.8             # VAE基础止盈倍数
C.VAE_初始止损 = 1.5           # VAE初始止损倍数
C.VAE_周期 = 20                # VAE计算周期

# 固定风控参数
C.固定止损 = 0.5               # 固定止损百分比 (0.5%)
```

### 2. 策略运行逻辑

**开仓条件（5个条件同时满足）**：
1. **SKDJ超卖确认**：K<20 且 D<20，确保在超卖区域入场
2. **双重背离确认**：CMF底背离 AND BIAS底背离（允许前1-2根K线的背离信号）
3. **强趋势确认**：ADX>40，确保在强趋势中交易，避免震荡市假信号
4. **突破确认**：收盘价刚刚突破阻力线，避免追高风险
5. **多层过滤机制**：所有条件同时满足才产生买入信号

**平仓条件（任一条件满足）**：
1. **固定止损**：亏损达到0.5%时强制止损
2. **VAE动态止盈**：根据市场波动率自适应调整止盈位
   - 低波动区：放宽止盈（2倍基础TR）
   - 正常波动区：标准止盈（1倍基础TR）
   - 高波动区：收紧止盈（1倍基础TR）
   - 极高波动区：严格止盈（0.7倍基础TR）

### 3. 监控和调试

策略运行中会自动显示：

```
🔍 CMF+BIAS双重背离策略开仓条件检查:
   📊 SKDJ超卖: K=15.2, D=18.5 (✅ 超卖确认)
   📈 CMF底背离: True (资金流背离确认)
   📉 BIAS底背离: True (乖离率背离确认)
   💪 ADX强趋势: 45.2 (✅ 强趋势确认)
   🚀 突破确认: True (阻力线突破)

🔍 VAE动态风控平仓条件检查:
   💰 当前价格: 10.50
   📊 入场价格: 10.00
   📈 盈亏比例: +5.00%
   🌊 波动率区间: 正常波动区 (ATR比值: 1.1)
   🎯 动态止盈线: 10.35
```

## 📊 CMF+BIAS双重背离策略工作原理

### 1. 核心架构（四层过滤机制）

- **第一层**：背离信号识别（CMF + BIAS双重背离）
- **第二层**：市场环境过滤（SKDJ超卖 + ADX强趋势）
- **第三层**：精准入场时机（阻力线突破确认）
- **第四层**：动态风险管理（VAE自适应止盈止损）

### 2. 背离信号类型

**CMF资金流背离**：
- **CMF底背离**：价格创新低但CMF未创新低，且CMF<0
- **CMF顶背离**：价格创新高但CMF未创新高，且CMF>0

**BIAS乖离率背离**：
- **BIAS底背离**：价格创新低但BIAS未创新低，且BIAS<0
- **BIAS顶背离**：价格创新高但BIAS未创新高，且BIAS>0

### 3. 完整交易逻辑

**多重确认机制**：
- 双重背离确认：CMF + BIAS同时背离，提高信号可靠性
- 时间窗口容错：允许前1-2根K线的背离信号，避免错过机会
- 趋势环境过滤：ADX>40确保在强趋势中交易，避免震荡市假信号

**精准入场时机**：
- SKDJ超卖确认：K<20且D<20，确保在超卖区域入场
- 阻力线突破：刚刚突破阻力线时入场，避免追高风险
- 多层过滤机制：5个条件同时满足才产生买入信号

**智能风险管理**：
- 固定止损保护：0.5%固定止损，控制单笔最大亏损
- VAE动态止盈：根据市场波动率自适应调整止盈位
- 波动率分区管理：低波动放宽止盈，高波动收紧止盈

## 🔧 技术实现细节

### 1. 核心指标计算模块

**SKDJ超卖指标**：
```python
# RSV计算
LOWV = LLV(LOW, SKDJ_N)
HIGHV = HHV(HIGH, SKDJ_N)
RSV = EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100, SKDJ_M)
K = EMA(RSV, SKDJ_M)
D = MA(K, SKDJ_M)
```

**CMF资金流背离**：
```python
# CMF计算
CLV = (CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW)
MF = CLV * VOL
CMF = SUM(MF, CMF_N) / SUM(VOL, CMF_N)

# 背离判断
CMF_HH = HIGH >= HHV(HIGH, CMF_M)
CMF_LL = LOW <= LLV(LOW, CMF_M)
CMF_指标HH = CMF >= HHV(CMF, CMF_M)
CMF_指标LL = CMF <= LLV(CMF, CMF_M)
CMF底背离 = CMF_LL AND CMF_指标LL==0 AND CMF<0
```

**VAE动态风控**：
```python
# 波动率分区
当前ATR = ATR(VAE_周期)
ATR均值 = MA(当前ATR, VAE_周期*2)
波动率比值 = 当前ATR / ATR均值

# 动态止盈调整
if 波动率比值 <= 0.8:      # 低波动区
    动态TR = VAE_基础TR * 2
elif 波动率比值 > 1.8:     # 极高波动区
    动态TR = VAE_基础TR * 0.7
else:                      # 正常/高波动区
    动态TR = VAE_基础TR
```

### 2. 集成架构

- **init函数**：完整的策略参数配置
- **calculate_technical_indicators**：集成所有技术指标计算
- **check_entry_conditions**：5层过滤的开仓逻辑
- **check_exit_conditions**：VAE动态风控的平仓逻辑
- **策略监控**：实时显示背离信号和风控状态

### 3. 兼容性保证

- ✅ 完全兼容原有6sk线框架
- ✅ 保留K线合成逻辑（2根K线合成1根）
- ✅ 保留QMT下单系统和账户管理
- ✅ 保留动态模式和标准模式切换

## ⚠️ 注意事项

1. **参数调优**：根据不同品种和周期调整各指标参数
2. **市场环境**：策略适用于强趋势市场，震荡市需谨慎
3. **风险控制**：严格执行0.5%固定止损，避免大幅亏损
4. **信号质量**：确保5个开仓条件同时满足，避免假信号

## 📈 策略优势

- **信号质量高**：多重过滤减少假信号，提高胜率
- **适应性强**：VAE模块自动适应市场波动
- **风险可控**：固定+动态双重止损机制
- **逻辑清晰**：每个组件都有明确的作用和边界

## 🚀 实施路线图

### 第一阶段：代码实现（1-2天）
1. 将完整策略代码整合到6sk线框架
2. 进行语法检查和基础测试
3. 确认所有指标计算正确
4. 验证买卖信号逻辑

### 第二阶段：历史回测（3-5天）
1. 选择代表性时间段进行回测
2. 分析不同市场环境下的表现
3. 统计胜率、盈亏比、最大回撤等关键指标
4. 识别策略的优势时段和弱势时段
- 动态模式初期可能数据不足，会自动禁用背离检测
- 建议在标准模式下使用以获得最佳效果

### 2. 参数调优

- MACD参数（12,26,9）适合大多数市场，但可根据具体股票调整
- 峰谷识别窗口大小影响信号敏感度
- 背离强度阈值需要根据实际效果调整

### 3. 性能考虑

- 背离计算会增加一定的计算负担
- 建议在高性能环境中运行
- 可以通过enable_divergence_strategy开关控制

## 🔄 渐进式部署建议

### 第一阶段：观察模式（1-2周）
```python
C.enable_divergence_strategy = True
C.divergence_min_strength = 'strong'  # 只使用强信号
# 观察背离信号的准确性和频率
```

### 第二阶段：辅助模式（2-4周）
```python
C.divergence_min_strength = 'medium'  # 使用中等以上信号
# 让背离信号参与交易决策
```

### 第三阶段：完全集成（长期）
```python
C.divergence_min_strength = 'weak'    # 使用所有信号
C.divergence_weight = 0.5             # 提高权重
# 根据实际效果持续优化参数
```

## 📞 技术支持

如果在使用过程中遇到问题：

1. 检查日志输出中的背离信号信息
2. 使用print_divergence_strategy_status(C)查看配置
3. 确认数据质量和数量是否充足
4. 可以临时禁用背离策略进行对比测试

## 🎯 总结

背离策略已经成功集成到6sk线框架中，完全保留了原有架构的稳定性和可靠性。通过MACD背离信号的增强，预期能够：

- 提高交易信号的质量
- 减少假突破的影响
- 增强真正突破的捕捉能力
- 保持系统的稳定性和兼容性

建议采用渐进式部署方式，逐步验证和优化参数，以获得最佳的交易效果。
