#coding:gbk
"""
测试QMT兼容版修复是否正确
"""

def test_qmt_compatibility():
    """测试QMT兼容版"""
    try:
        print("🔍 测试QMT兼容版修复...")

        # 读取并执行文件
        with open('框架/6sk线_QMT兼容版.py', 'r', encoding='gbk') as f:
            code = f.read()

        # 执行代码
        exec(code)

        # 创建模拟的C对象 - 模拟QMT环境
        class MockC:
            def __init__(self):
                self.stockcode = "000001"
                self.market = "SZ"
                self.period = "1m"
                self.stock = self.stockcode + '.' + self.market

            def get_market_data_ex(self, fields, stocks, period, count, subscribe):
                """模拟QMT的get_market_data_ex方法"""
                print(f"📊 模拟QMT API调用: fields={fields}, stocks={stocks}")
                return {
                    self.stock: {
                        'lastPrice': [10.25],
                        'volume': [1500000]
                    }
                }

        # 测试get_safe_market_data函数
        C = MockC()
        market_data = get_safe_market_data(C, "2024-01-01 09:30:00")
        if market_data:
            price, volume = market_data
            print(f"✅ get_safe_market_data测试成功: price={price}, volume={volume}")
        else:
            print("❌ get_safe_market_data测试失败")

        # 测试备用函数
        price = safe_get_price(C)
        volume = safe_get_volume(C)
        print(f"✅ 备用函数测试成功: price={price}, volume={volume}")

        # 测试检测器类
        detector = CompleteCMFBIASDivergenceDetector()
        print(f"✅ 检测器类创建成功: {type(detector)}")

        print("🎉 所有QMT兼容性测试通过！")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_qmt_compatibility()
