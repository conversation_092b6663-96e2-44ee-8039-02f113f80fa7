#coding:gbk

"""
行业轮动回测策略
================

策略概述：
本策略是一个基于行业动量的轮动投资策略，通过定期评估不同行业的表现来进行资产配置。

策略原理：
1. 行业选择：覆盖6个主要行业指数
2. 动量评估：计算各行业过去20个交易日的收益率
3. 强势筛选：选择收益率最高的行业
4. 个股精选：在强势行业中选择市值最大的5只股票
5. 定期调仓：每月重新评估和调整持仓

行业覆盖：
- 1000能源 (399381.SZ)：能源行业代表性指数
- 1000材料 (399382.SZ)：原材料行业指数
- 1000工业 (399383.SZ)：工业制造业指数
- 1000可选 (399384.SZ)：可选消费行业指数
- 1000消费 (399385.SZ)：必需消费行业指数
- 1000医药 (399386.SZ)：医药生物行业指数

选股逻辑：
1. 行业动量：选择20日收益率最高的行业
2. 市值优先：在该行业中选择市值最大的股票
3. 集中持仓：持有5只股票，每只占比16%（0.8/5）
4. 月度调仓：每月重新评估和调整

策略特点：
- 行业轮动：捕捉不同行业的轮动机会
- 动量策略：追踪强势行业的持续性
- 市值偏好：选择流动性好的大市值股票
- 风险分散：在单一行业内分散持仓

适用环境：
- 股票指数日线数据
- 回测模式（非实盘交易）
- 中长期投资策略
- 行业轮动明显的市场环境

风险控制：
- 行业集中风险：仅投资单一行业
- 市值偏向：可能错过小市值成长股
- 动量风险：强势行业可能反转
- 调仓成本：月度调仓产生交易成本

注意事项：
- 本策略仅用于回测研究，不适用于实盘交易
- 需要完整的行业指数和成分股数据
- 建议结合宏观经济周期进行优化
- 交易成本对策略收益有重要影响
"""

# 导入必要的数值计算库
import numpy as np  # 数值计算库，用于统计计算
import math         # 数学函数库，用于数学运算

def init(ContextInfo):
    """
    行业轮动策略初始化函数

    功能：设置策略的基本参数和股票池

    参数：
        ContextInfo: 策略运行环境对象

    初始化内容：
    1. 设置持仓跟踪字典
    2. 定义行业指数列表
    3. 获取所有行业成分股
    4. 配置策略参数
    5. 设置回测账户

    关键变量：
        MarketPosition: 持仓跟踪字典
        index_universe: 行业指数列表
        day: 动量计算周期（20日）
        ratio: 总仓位比例（80%）
        holding_amount: 持仓股票数量（5只）
    """

    # === 第一步：初始化持仓跟踪系统 ===

    # 创建持仓跟踪字典，用于记录各股票的持仓状态
    MarketPosition = {}
    ContextInfo.MarketPosition = MarketPosition

    # === 第二步：定义行业指数池 ===

    # 设置6个主要行业指数代码
    # 这些指数代表了A股市场的主要行业分类
    index_universe = [
        '399381.SZ',  # 1000能源：石油石化、煤炭等能源行业
        '399382.SZ',  # 1000材料：化工、钢铁、有色金属等原材料
        '399383.SZ',  # 1000工业：机械设备、电气设备等工业制造
        '399384.SZ',  # 1000可选：汽车、家电、纺织服装等可选消费
        '399385.SZ',  # 1000消费：食品饮料、商业贸易等必需消费
        '399386.SZ'   # 1000医药：医药生物、医疗器械等医疗健康
    ]

    # === 第三步：构建完整股票池 ===

    # 初始化成分股列表
    index_stocks = []

    # 遍历每个行业指数，获取其成分股
    for index in index_universe:
        # 获取该行业指数的所有成分股
        sector_stocks = ContextInfo.get_sector(index)

        # 将成分股添加到总列表中
        for stock in sector_stocks:
            index_stocks.append(stock)

    # 设置策略的股票池：行业指数 + 所有成分股
    # 这样既能获取指数数据计算收益率，又能交易具体的成分股
    ContextInfo.set_universe(index_universe + index_stocks)

    # === 第四步：设置策略参数 ===

    # 设置动量计算周期（20个交易日）
    ContextInfo.day = 20

    # 设置总仓位比例（80%，保留20%现金）
    ContextInfo.ratio = 0.8

    # 设置持仓股票数量（5只股票）
    ContextInfo.holding_amount = 5

    # === 第五步：设置回测账户 ===

    # 设置回测账户ID
    ContextInfo.accountID = 'testS'

def handlebar(ContextInfo):
    """
    行业轮动策略核心处理函数

    功能：执行基于行业动量的轮动投资策略

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 检查是否到达月度调仓时点
    2. 计算各行业指数的20日收益率
    3. 选择收益率最高的行业
    4. 获取该行业成分股的市值数据
    5. 选择市值最大的5只股票
    6. 执行调仓操作

    调仓逻辑：
    - 调仓频率：每月一次（月份变化时触发）
    - 行业选择：20日收益率最高的行业
    - 个股选择：该行业市值最大的5只股票
    - 仓位分配：每只股票16%（0.8/5）
    """

    # === 第一步：初始化变量和获取时间信息 ===

    # 初始化交易条件标志
    buy_condition = False   # 买入条件
    sell_condition = False  # 卖出条件

    # 获取当前K线位置
    d = ContextInfo.barpos

    # 获取前一日和当前日期
    lastdate = timetag_to_datetime(ContextInfo.get_bar_timetag(d - 1), '%Y%m%d')
    date = timetag_to_datetime(ContextInfo.get_bar_timetag(d), '%Y%m%d')

    # 输出当前处理日期
    print(date)

    # === 第二步：设置策略参数 ===

    # 定义行业指数列表（与init中保持一致）
    index_list = ['399381.SZ', '399382.SZ', '399383.SZ',
                  '399384.SZ', '399385.SZ', '399386.SZ']

    # 初始化行业收益率列表
    return_index = []

    # 计算单只股票的目标权重
    # 总仓位80%，分配给5只股票，每只16%
    weight = ContextInfo.ratio / ContextInfo.holding_amount

    # 初始化市值字典
    size_dict = {}

    # === 第三步：月度调仓时点判断 ===

    # 检查是否跨月（月份发生变化）
    # 通过比较当前日期和前一日期的月份部分（YYYYMMDD中的MM部分）
    if float(date[-4:-2]) != float(lastdate[-4:-2]):

        print("=" * 80)
        print(f"触发月度调仓 - 当前日期: {date}, 月份: {date[-4:-2]}")
        print("=" * 80)

        # === 第四步：获取历史数据并计算行业收益率 ===

        # 获取过去21个交易日的收盘价数据（用于计算20日收益率）
        his = ContextInfo.get_history_data(21, '1d', 'close')

        # 清理无效数据（移除空数据的品种）
        for k in list(his.keys()):
            if len(his[k]) == 0:
                del his[k]

        # === 第五步：计算各行业指数的动量收益率 ===

        print("\n各行业20日收益率:")
        print("-" * 40)

        # 遍历每个行业指数计算收益率
        for index in index_list:
            ratio = 0  # 初始化收益率

            try:
                # 计算20日收益率
                # 公式：(最新价格 - 20日前价格) / 20日前价格
                # his[index][-2]: 前一日收盘价（最新完整交易日）
                # his[index][0]: 21日前收盘价（20个交易日前）
                ratio = (his[index][-2] - his[index][0]) / his[index][0]
			except KeyError:
				print('key error:' + index)
			except IndexError:
				print('list index out of range:' + index)
			return_index.append(ratio)
		# 获取指定数内收益率表现最好的行业
		best_index = index_list[np.argmax(return_index)]
		#print '当前最佳行业是：', ContextInfo.get_stock_name(best_index)[3:]+'行业'
		# 获取当天有交易的股票
		index_stock = ContextInfo.get_sector(best_index)
		stock_available = []
		for stock in index_stock:
			if ContextInfo.is_suspended_stock(stock) == False:
				stock_available.append(stock)
				
		for stock in stock_available:
			if stock in list(his.keys()):
				#目前历史流通股本取不到，暂用总股本
				if len(his[stock]) >= 2:
					stocksize =his[stock][-2] * float(ContextInfo.get_financial_data(['CAPITALSTRUCTURE.total_capital'],[stock],lastdate,date).iloc[0,-1])
					size_dict[stock] = stocksize
				elif len(his[stock]) == 1:
					stocksize =his[stock][-1] * float(ContextInfo.get_financial_data(['CAPITALSTRUCTURE.total_capital'],[stock],lastdate,date).iloc[0,-1])
					size_dict[stock] = stocksize
				else:
					return
		size_sorted = sorted(list(size_dict.items()), key = lambda item:item[1])
		pre_holding = []
		
		for tuple in size_sorted[-ContextInfo.holding_amount:]:
			pre_holding.append(tuple[0])
		#print '买入备选',pre_holding
		#函数下单
		if len(pre_holding) > 0:
			sellshort_list = []
			for stock in list(ContextInfo.MarketPosition.keys()):
				if stock not in pre_holding and (stock in list(his.keys())):
					order_shares(stock,-ContextInfo.MarketPosition[stock],'lastest',his[stock][-1],ContextInfo,ContextInfo.accountID)
					print('sell',stock)
					sell_condition = True
					sellshort_list.append(stock)
			if len(sellshort_list) >0:
				for stock in sellshort_list:
					del ContextInfo.MarketPosition[stock]
			for stock in pre_holding:
				if stock not in list(ContextInfo.MarketPosition.keys()):
					Lots = math.floor(ContextInfo.ratio * (1.0/len(pre_holding)) * ContextInfo.capital / (his[stock][-1] * 100))
					order_shares(stock,Lots *100,'lastest',his[stock][-1],ContextInfo,ContextInfo.accountID)
					print('buy',stock)
					buy_condition = True
					ContextInfo.MarketPosition[stock] = Lots *100

	#ContextInfo.paint('do_buy', int(buy_condition), -1, 0)
	#ContextInfo.paint('do_sell', int(sell_condition), -1, 0)




