#coding:gbk

"""
双均线回测策略示例
==================

策略概述：
本策略基于经典的双移动平均线交叉系统，通过计算快速和慢速移动平均线的交叉信号来进行交易决策。

策略原理：
- 金叉（快线上穿慢线）：产生买入信号，开仓买入
- 死叉（快线下穿慢线）：产生卖出信号，平仓卖出

技术指标：
- 快线：10日简单移动平均线
- 慢线：20日简单移动平均线

适用场景：
- 回测环境下的策略验证
- 趋势跟踪策略的基础框架
- 技术分析入门学习

使用方法：
1. 点击回测运行
2. 在主图选择要交易的股票品种
3. 设置回测时间范围
4. 查看回测结果和绩效指标

注意事项：
- 本示例仅用于回测，不适用于实盘交易
- 回测结果不代表未来实际收益
- 建议结合其他技术指标优化策略
"""

# 导入常用数据处理和技术分析库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算和统计计算
import talib         # 技术分析库，提供各种技术指标计算函数

def init(C):
    """
    策略初始化函数

    功能：设置策略的基本参数和配置信息

    参数：
        C: ContextInfo对象（可缩写为C），包含策略运行环境的所有信息
           - stockcode: 股票代码（不含市场后缀）
           - market: 市场代码（如SH、SZ）
           - period: K线周期

    设置的参数：
        C.stock: 完整的股票代码（格式：代码.市场）
        C.line1: 快速移动平均线周期
        C.line2: 慢速移动平均线周期
        C.accountid: 回测账户ID

    说明：
        - init函数在策略启动时执行一次
        - 用于初始化策略参数、变量和配置
        - 回测模式下账户ID可以是任意字符串
    """
    # 构建完整的股票代码，格式：股票代码.市场代码（如：000001.SZ）
    C.stock = C.stockcode + '.' + C.market

    # 设置双均线系统的参数
    C.line1 = 10   # 快速移动平均线周期（短期趋势），单位：交易日
    C.line2 = 20   # 慢速移动平均线周期（长期趋势），单位：交易日

    # 设置回测账户ID，回测模式下可以使用任意字符串作为账户标识
    C.accountid = "testS"

def handlebar(C):
    """
    K线数据处理函数（策略核心逻辑）

    功能：在每根K线完成时执行双均线交易策略

    参数：
        C: ContextInfo对象，包含当前K线位置、市场数据等信息

    执行流程：
    1. 获取当前K线时间
    2. 获取历史收盘价数据
    3. 计算快慢双均线
    4. 获取账户和持仓信息
    5. 根据均线交叉信号执行交易
    6. 在图表上标记交易点位

    交易逻辑：
    - 开仓条件：无持仓 + 快线 > 慢线（多头信号）
    - 平仓条件：有持仓 + 快线 < 慢线（空头信号）

    注意：
    - 本函数在每根K线完成时被调用
    - 使用历史数据进行回测，不订阅实时行情
    - 交易信号会在图表上显示标记
    """

    # === 第一步：获取当前K线时间信息 ===

    # 获取当前K线的时间戳并转换为可读格式
    # C.barpos: 当前K线在数据序列中的位置索引
    # C.get_bar_timetag(): 获取指定位置K线的时间戳
    # timetag_to_datetime(): 将时间戳转换为日期时间字符串
    bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')

    # === 第二步：获取历史价格数据 ===

    # 获取历史收盘价数据用于计算移动平均线
    # 参数说明：
    # ['close']: 只获取收盘价字段
    # [C.stock]: 目标股票列表
    # end_time: 数据结束时间（当前K线时间）
    # period: K线周期（与策略设置保持一致）
    # count: 获取数据的数量（取两个均线周期的最大值）
    # subscribe=False: 不订阅实时数据，使用本地历史数据（回测模式下速度更快）
    local_data = C.get_market_data_ex(['close'], [C.stock], end_time=bar_date, period=C.period, count=max(C.line1, C.line2), subscribe=False)

    # 提取收盘价序列并转换为列表格式
    close_list = list(local_data[C.stock].iloc[:, 0])

    # === 第三步：数据有效性检查和均线计算 ===

    # 检查数据是否充足，如果数据不足则跳过本次处理
    if len(close_list) < 1:
        print(bar_date, '行情不足 跳过')
        return

    # 计算快速移动平均线（短期趋势）
    # 取最近C.line1个交易日的收盘价平均值，保留2位小数
    line1_mean = round(np.mean(close_list[-C.line1:]), 2)

    # 计算慢速移动平均线（长期趋势）
    # 取最近C.line2个交易日的收盘价平均值，保留2位小数
    line2_mean = round(np.mean(close_list[-C.line2:]), 2)

    # 输出当前均线数值，用于监控策略状态
    print(f"{bar_date} 短均线{line1_mean} 长均线{line2_mean}")

    # === 第四步：获取账户和持仓信息 ===

    # 获取回测账户信息
    # 'test': 回测模式下的固定账户标识
    # 'stock': 账户类型（股票账户）
    # 'account': 查询账户资金信息
    account = get_trade_detail_data('test', 'stock', 'account')
    account = account[0]  # 取第一个账户（通常只有一个）

    # 获取可用资金，转换为整数便于计算
    available_cash = int(account.m_dAvailable)

    # 获取当前持仓信息
    holdings = get_trade_detail_data('test', 'stock', 'position')

    # 将持仓信息转换为字典格式：{股票代码: 持仓数量}
    holdings = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nVolume for i in holdings}

    # 获取目标股票的当前持仓数量，如果没有持仓则为0
    holding_vol = holdings[C.stock] if C.stock in holdings else 0

    # === 第五步：交易信号判断和执行 ===

    # 开仓条件：当前无持仓 且 快线在慢线上方（多头信号）
    if holding_vol == 0 and line1_mean > line2_mean:

        # 计算买入数量：根据可用资金和当前价格计算
        # 向下取整到100股的整数倍（A股最小交易单位为100股）
        vol = int(available_cash / close_list[-1] / 100) * 100

        # 执行买入操作
        # 参数说明：
        # 23: 买入操作代码
        # 1101: 委托类型（普通委托）
        # C.accountid: 账户ID
        # C.stock: 股票代码
        # 5: 价格类型（市价）
        # -1: 价格（-1表示使用价格类型设定）
        # vol: 委托数量
        # C: ContextInfo对象
        passorder(23, 1101, C.accountid, C.stock, 5, -1, vol, C)

        # 输出交易信息
        print(f"{bar_date} 开仓")

        # 在图表上标记开仓点位
        # 参数：(是否显示, 显示位置, 显示文字)
        C.draw_text(1, 1, '开')

    # 平仓条件：当前有持仓 且 快线在慢线下方（空头信号）
    elif holding_vol > 0 and line1_mean < line2_mean:

        # 更新持仓状态标记
        C.holding = False

        # 执行卖出操作（全部平仓）
        # 24: 卖出操作代码
        # holding_vol: 卖出全部持仓数量
        passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)

        # 输出交易信息
        print(f"{bar_date} 平仓")

        # 在图表上标记平仓点位
        C.draw_text(1, 1, '平')
