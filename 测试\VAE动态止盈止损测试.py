#coding:gbk

"""
VAE动态止盈止损逻辑测试
测试基于实际市场波动的动态止盈止损计算
"""

import numpy as np
import sys
import os

# 添加框架路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '框架'))

# 导入策略模块
from 6sk线 import CMFBIASDivergenceDetector

def test_vae_dynamic_control():
    """
    测试VAE动态风控的新逻辑
    """
    print("="*80)
    print("🧪 VAE动态止盈止损逻辑测试")
    print("="*80)
    
    # 创建检测器实例
    detector = CMFBIASDivergenceDetector(
        VAE_基础TR=1.5,
        VAE_初始止损=1.5,
        VAE_周期=20
    )
    
    # 测试场景1：低波动市场
    print("\n📊 测试场景1：低波动市场 (ATR=0.5%, 价格=10元)")
    test_low_volatility_market(detector)
    
    # 测试场景2：正常波动市场
    print("\n📊 测试场景2：正常波动市场 (ATR=1.5%, 价格=10元)")
    test_normal_volatility_market(detector)
    
    # 测试场景3：高波动市场
    print("\n📊 测试场景3：高波动市场 (ATR=3.0%, 价格=10元)")
    test_high_volatility_market(detector)
    
    # 测试场景4：极高波动市场
    print("\n📊 测试场景4：极高波动市场 (ATR=5.0%, 价格=10元)")
    test_extreme_volatility_market(detector)
    
    # 对比分析
    print("\n📋 新旧逻辑对比分析")
    compare_old_vs_new_logic()

def test_low_volatility_market(detector):
    """测试低波动市场"""
    # 模拟低波动数据：ATR约0.5%，价格10元
    price = 10.0
    atr_value = 0.05  # 0.5%
    
    # 构造数据：价格在9.8-10.2之间小幅波动
    highs = np.array([10.1, 10.0, 10.2, 10.1, 10.0] * 10)
    lows = np.array([9.9, 9.8, 10.0, 9.9, 9.8] * 10)
    closes = np.array([10.0, 9.9, 10.1, 10.0, 9.9] * 10)
    
    result = detector.calculate_VAE_dynamic_control(highs, lows, closes)
    
    print(f"   波动率区间: {result['波动率区间']}")
    print(f"   实际波动幅度: {result['实际波动幅度']:.2f}%")
    print(f"   动态止盈比例: {result['动态止盈比例']:.2f}%")
    print(f"   动态止损比例: {result['动态止损比例']:.2f}%")
    print(f"   止盈系数: {result['止盈系数']:.1f}")
    print(f"   止损系数: {result['止损系数']:.1f}")
    print(f"   传统动态TR: {result['动态TR']:.1f}%")

def test_normal_volatility_market(detector):
    """测试正常波动市场"""
    # 模拟正常波动数据：ATR约1.5%，价格10元
    highs = np.array([10.3, 10.1, 10.4, 10.2, 10.1] * 10)
    lows = np.array([9.7, 9.8, 9.6, 9.8, 9.9] * 10)
    closes = np.array([10.0, 9.9, 10.2, 10.0, 10.1] * 10)
    
    result = detector.calculate_VAE_dynamic_control(highs, lows, closes)
    
    print(f"   波动率区间: {result['波动率区间']}")
    print(f"   实际波动幅度: {result['实际波动幅度']:.2f}%")
    print(f"   动态止盈比例: {result['动态止盈比例']:.2f}%")
    print(f"   动态止损比例: {result['动态止损比例']:.2f}%")
    print(f"   止盈系数: {result['止盈系数']:.1f}")
    print(f"   止损系数: {result['止损系数']:.1f}")
    print(f"   传统动态TR: {result['动态TR']:.1f}%")

def test_high_volatility_market(detector):
    """测试高波动市场"""
    # 模拟高波动数据：ATR约3.0%，价格10元
    highs = np.array([10.6, 10.4, 10.8, 10.5, 10.3] * 10)
    lows = np.array([9.4, 9.6, 9.2, 9.5, 9.7] * 10)
    closes = np.array([10.0, 9.8, 10.4, 10.1, 9.9] * 10)
    
    result = detector.calculate_VAE_dynamic_control(highs, lows, closes)
    
    print(f"   波动率区间: {result['波动率区间']}")
    print(f"   实际波动幅度: {result['实际波动幅度']:.2f}%")
    print(f"   动态止盈比例: {result['动态止盈比例']:.2f}%")
    print(f"   动态止损比例: {result['动态止损比例']:.2f}%")
    print(f"   止盈系数: {result['止盈系数']:.1f}")
    print(f"   止损系数: {result['止损系数']:.1f}")
    print(f"   传统动态TR: {result['动态TR']:.1f}%")

def test_extreme_volatility_market(detector):
    """测试极高波动市场"""
    # 模拟极高波动数据：ATR约5.0%，价格10元
    highs = np.array([11.0, 10.8, 11.2, 10.9, 10.6] * 10)
    lows = np.array([9.0, 9.2, 8.8, 9.1, 9.4] * 10)
    closes = np.array([10.0, 9.5, 10.5, 10.2, 9.8] * 10)
    
    result = detector.calculate_VAE_dynamic_control(highs, lows, closes)
    
    print(f"   波动率区间: {result['波动率区间']}")
    print(f"   实际波动幅度: {result['实际波动幅度']:.2f}%")
    print(f"   动态止盈比例: {result['动态止盈比例']:.2f}%")
    print(f"   动态止损比例: {result['动态止损比例']:.2f}%")
    print(f"   止盈系数: {result['止盈系数']:.1f}")
    print(f"   止损系数: {result['止损系数']:.1f}")
    print(f"   传统动态TR: {result['动态TR']:.1f}%")

def compare_old_vs_new_logic():
    """对比新旧逻辑"""
    print("\n   场景对比分析:")
    print("   " + "="*60)
    print("   市场环境    | 旧逻辑止盈 | 新逻辑止盈 | 旧逻辑止损 | 新逻辑止损")
    print("   " + "-"*60)
    print("   低波动(0.5%)| 3.0%      | ~0.35%    | 1.5%      | ~0.20%")
    print("   正常波动(1.5%)| 1.5%      | ~1.20%    | 1.5%      | ~0.75%")
    print("   高波动(3.0%) | 1.5%      | ~2.70%    | 1.5%      | ~1.80%")
    print("   极高波动(5.0%)| 1.05%     | ~5.00%    | 1.5%      | ~3.50%")
    print("   " + "="*60)
    
    print("\n   💡 新逻辑优势:")
    print("   1. 低波动时：目标更现实，容易达到")
    print("   2. 高波动时：充分利用市场机会")
    print("   3. 止损也动态调整：高波动时避免被震出")
    print("   4. 风险收益比更合理：始终保持止盈>止损")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 边界情况测试")
    print("-"*40)
    
    detector = CMFBIASDivergenceDetector()
    
    # 测试空数据
    result = detector.calculate_VAE_dynamic_control([], [], [])
    print(f"空数据测试: {result['波动率区间']}, 止盈={result['动态止盈比例']:.2f}%")
    
    # 测试极小ATR
    tiny_data = np.array([10.0001, 10.0002, 10.0001] * 20)
    result = detector.calculate_VAE_dynamic_control(tiny_data, tiny_data, tiny_data)
    print(f"极小波动测试: {result['波动率区间']}, 止盈={result['动态止盈比例']:.2f}%")

if __name__ == "__main__":
    test_vae_dynamic_control()
    test_edge_cases()
    
    print("\n✅ 测试完成")
    print("💡 建议：在实盘使用前，请根据具体标的的历史波动特征调整系数")
