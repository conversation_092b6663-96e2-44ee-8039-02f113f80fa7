"""
CMF+BIAS双重背离策略性能测试
========================================================================

测试目标：
1. 对比原版与优化版的性能差异
2. 验证计算结果的一致性
3. 分析内存使用情况
4. 测试不同数据量下的性能表现
"""

import time
import numpy as np
import pandas as pd
import tracemalloc
from typing import Dict, List
import sys
import os

# 添加路径以导入策略文件
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.original_detector = None
        self.optimized_detector = None
        self.test_results = {}
        
    def setup_detectors(self):
        """初始化检测器"""
        try:
            self.original_detector = OriginalDetector()
            self.optimized_detector = OptimizedDetector()
            print("✅ 检测器初始化成功")
        except Exception as e:
            print(f"❌ 检测器初始化失败: {e}")
            return False
        return True
    
    def generate_test_data(self, num_bars: int) -> List[Dict]:
        """生成测试用的K线数据"""
        np.random.seed(42)  # 固定随机种子确保可重复性
        
        base_price = 100.0
        klines = []
        
        for i in range(num_bars):
            # 模拟价格波动
            change = np.random.normal(0, 0.02)  # 2%的标准波动
            base_price *= (1 + change)
            
            # 生成OHLC数据
            high = base_price * (1 + abs(np.random.normal(0, 0.01)))
            low = base_price * (1 - abs(np.random.normal(0, 0.01)))
            close = base_price
            open_price = klines[-1]['close'] if klines else base_price
            volume = np.random.randint(1000, 10000)
            
            klines.append({
                'open': open_price,
                'high': max(high, open_price, close),
                'low': min(low, open_price, close),
                'close': close,
                'volume': volume,
                'timestamp': f"2024-01-{i+1:02d} 09:30:00"
            })
            
        return klines
    
    def measure_performance(self, detector, test_data: List[Dict], test_name: str, iterations: int = 1) -> Dict:
        """测量性能指标"""
        
        # 开始内存监控
        tracemalloc.start()
        
        # 预热
        try:
            detector.get_comprehensive_signals(test_data)
        except:
            pass
        
        # 性能测试
        start_time = time.perf_counter()
        results = []
        
        for i in range(iterations):
            try:
                if hasattr(detector, 'get_comprehensive_signals_optimized'):
                    result = detector.get_comprehensive_signals_optimized(test_data)
                else:
                    result = detector.get_comprehensive_signals(test_data)
                results.append(result)
            except Exception as e:
                print(f"❌ {test_name} 执行失败: {e}")
                results.append({'status': 'error', 'error': str(e)})
        
        end_time = time.perf_counter()
        
        # 内存使用情况
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # 计算性能指标
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        return {
            'test_name': test_name,
            'total_time': total_time,
            'avg_time': avg_time,
            'iterations': iterations,
            'memory_current': current / 1024 / 1024,  # MB
            'memory_peak': peak / 1024 / 1024,  # MB
            'results': results[-1] if results else None,  # 最后一次结果
            'success_rate': sum(1 for r in results if r.get('status') == 'success') / len(results)
        }
    
    def compare_results(self, original_result: Dict, optimized_result: Dict) -> Dict:
        """比较计算结果的一致性"""
        comparison = {
            'indicators_match': True,
            'conditions_match': True,
            'signals_match': True,
            'differences': []
        }
        
        try:
            # 比较指标值
            if 'indicators' in original_result and 'indicators' in optimized_result:
                orig_indicators = original_result['indicators']
                opt_indicators = optimized_result['indicators']
                
                for key in orig_indicators:
                    if key in opt_indicators:
                        orig_val = orig_indicators[key]
                        opt_val = opt_indicators[key]
                        
                        # 允许小的数值误差
                        if abs(orig_val - opt_val) > 1e-6:
                            comparison['indicators_match'] = False
                            comparison['differences'].append(f"指标{key}: 原版={orig_val:.6f}, 优化版={opt_val:.6f}")
            
            # 比较信号
            orig_buy = original_result.get('buy_signal', False)
            opt_buy = optimized_result.get('buy_signal', False)
            
            if orig_buy != opt_buy:
                comparison['signals_match'] = False
                comparison['differences'].append(f"买入信号: 原版={orig_buy}, 优化版={opt_buy}")
                
        except Exception as e:
            comparison['error'] = str(e)
            
        return comparison
    
    def run_performance_tests(self):
        """运行完整的性能测试"""
        print("🚀 开始性能测试...")
        print("=" * 60)
        
        if not self.setup_detectors():
            return
        
        # 测试场景
        test_scenarios = [
            {'name': '小数据量', 'bars': 100, 'iterations': 10},
            {'name': '中数据量', 'bars': 500, 'iterations': 5},
            {'name': '大数据量', 'bars': 1000, 'iterations': 3},
            {'name': '重复调用', 'bars': 200, 'iterations': 20}
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 测试场景: {scenario['name']} ({scenario['bars']}根K线, {scenario['iterations']}次迭代)")
            print("-" * 50)
            
            # 生成测试数据
            test_data = self.generate_test_data(scenario['bars'])
            
            # 测试原版
            print("🔄 测试原版策略...")
            original_perf = self.measure_performance(
                self.original_detector, test_data, 
                f"原版_{scenario['name']}", scenario['iterations']
            )
            
            # 测试优化版
            print("🔄 测试优化版策略...")
            optimized_perf = self.measure_performance(
                self.optimized_detector, test_data,
                f"优化版_{scenario['name']}", scenario['iterations']
            )
            
            # 性能对比
            speedup = original_perf['avg_time'] / optimized_perf['avg_time'] if optimized_perf['avg_time'] > 0 else 0
            memory_reduction = (original_perf['memory_peak'] - optimized_perf['memory_peak']) / original_perf['memory_peak'] * 100
            
            print(f"📈 性能对比结果:")
            print(f"   原版平均耗时: {original_perf['avg_time']*1000:.2f}ms")
            print(f"   优化版平均耗时: {optimized_perf['avg_time']*1000:.2f}ms")
            print(f"   性能提升: {speedup:.2f}x")
            print(f"   内存使用 - 原版: {original_perf['memory_peak']:.2f}MB, 优化版: {optimized_perf['memory_peak']:.2f}MB")
            print(f"   内存优化: {memory_reduction:.1f}%")
            
            # 结果一致性检查
            if original_perf['results'] and optimized_perf['results']:
                comparison = self.compare_results(original_perf['results'], optimized_perf['results'])
                if comparison['indicators_match'] and comparison['signals_match']:
                    print(f"   ✅ 计算结果一致")
                else:
                    print(f"   ⚠️ 计算结果存在差异:")
                    for diff in comparison['differences']:
                        print(f"      {diff}")
            
            # 保存测试结果
            self.test_results[scenario['name']] = {
                'original': original_perf,
                'optimized': optimized_perf,
                'speedup': speedup,
                'memory_reduction': memory_reduction,
                'consistency': comparison if 'comparison' in locals() else None
            }
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 性能测试报告")
        print("=" * 60)
        
        total_speedup = []
        total_memory_reduction = []
        
        for scenario_name, results in self.test_results.items():
            speedup = results['speedup']
            memory_reduction = results['memory_reduction']
            
            total_speedup.append(speedup)
            total_memory_reduction.append(memory_reduction)
            
            print(f"\n{scenario_name}:")
            print(f"  性能提升: {speedup:.2f}x")
            print(f"  内存优化: {memory_reduction:.1f}%")
            print(f"  原版成功率: {results['original']['success_rate']*100:.1f}%")
            print(f"  优化版成功率: {results['optimized']['success_rate']*100:.1f}%")
        
        # 总体统计
        avg_speedup = np.mean(total_speedup)
        avg_memory_reduction = np.mean(total_memory_reduction)
        
        print(f"\n🎯 总体优化效果:")
        print(f"  平均性能提升: {avg_speedup:.2f}x")
        print(f"  平均内存优化: {avg_memory_reduction:.1f}%")
        
        if avg_speedup >= 2.0:
            print(f"  🎉 优化效果优秀！达到预期目标")
        elif avg_speedup >= 1.5:
            print(f"  ✅ 优化效果良好")
        else:
            print(f"  ⚠️ 优化效果有限，需要进一步改进")

if __name__ == "__main__":
    # 运行性能测试
    test_suite = PerformanceTestSuite()
    test_suite.run_performance_tests()
