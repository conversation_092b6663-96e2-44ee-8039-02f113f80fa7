#coding:gbk
#!/usr/bin/python

"""
机器学习股票预测回测策略
========================

策略概述：
本策略是一个基于支持向量机(SVM)的股票价格预测模型，通过机器学习技术预测股票未来的涨跌方向。

模型特点：
- 算法：支持向量机(SVM)分类器
- 预测目标：5个交易日后的涨跌方向
- 特征窗口：过去15个交易日的市场数据
- 交易频率：每周一预测，周五验证

特征工程：
1. 相对收盘价：当前收盘价 / 15日平均收盘价
2. 相对成交量：当前成交量 / 15日平均成交量
3. 相对最高价：当前最高价 / 15日平均最高价
4. 相对最低价：当前最低价 / 15日平均最低价
5. 总收益率：15日期间的累计收益率
6. 价格波动率：15日收盘价的标准差

训练数据：
- 时间范围：2016年1月1日 - 2017年1月1日（1年历史数据）
- 样本构建：滑动窗口方式生成训练样本
- 标签定义：5日后涨跌（1=上涨，0=下跌）

交易逻辑：
- 预测时机：每周一进行预测
- 开仓条件：预测结果为上涨
- 平仓条件：预测结果为下跌或持有5日后
- 仓位管理：全仓买入/卖出

适用场景：
- 单只股票的中短期预测
- 机器学习策略验证
- 量化投资研究

注意事项：
- 本策略仅用于回测研究，不适用于实盘交易
- 机器学习模型存在过拟合风险
- 历史表现不代表未来收益
- 建议结合其他技术指标使用
"""

# 导入必要的数据处理和机器学习库
import pandas as pd      # 数据处理库，用于处理时间序列数据
import numpy as np       # 数值计算库，用于数学运算和统计分析
import time              # 时间处理库，用于时间相关操作
from datetime import *   # 日期时间库，用于日期处理
from sklearn import svm  # 机器学习库，使用支持向量机算法
import traceback         # 异常处理库，用于错误调试

def init(ContextInfo):
    """
    策略初始化函数

    功能：设置机器学习策略的基本参数和初始状态

    参数：
        ContextInfo: 策略运行环境对象

    初始化内容：
    1. 设置交易标的（单只股票）
    2. 初始化持仓和资金状态
    3. 设置训练标志和账户信息

    关键变量：
        stock: 目标股票代码
        holding: 持仓状态（0=无持仓，1=有持仓）
        days: 训练状态标志（0=未训练，1=已训练）
        money: 可用资金
    """

    # 构建完整的股票代码（代码.市场）
    ContextInfo.stock = ContextInfo.stockcode + '.' + ContextInfo.market

    # 设置股票池为单只目标股票
    ContextInfo.set_universe([ContextInfo.stock])

    # 初始化持仓状态（0=无持仓，1=有持仓）
    ContextInfo.holding = 0

    # 初始化训练状态标志（0=未训练，1=已训练）
    ContextInfo.days = 0

    # 初始化可用资金为初始资本
    ContextInfo.money = ContextInfo.capital

    # 设置回测账户ID
    ContextInfo.accountid = "testS"

def handlebar(ContextInfo):
    """
    机器学习策略核心处理函数

    功能：执行SVM模型训练和基于预测结果的交易决策

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 首次运行时进行SVM模型训练
    2. 获取当前市场数据并提取特征
    3. 使用训练好的模型进行预测
    4. 根据预测结果执行交易操作

    训练过程：
    - 使用2016年全年数据作为训练集
    - 构建特征向量和标签向量
    - 训练SVM分类器

    预测交易：
    - 每周一进行预测
    - 根据预测结果决定买卖
    """

    # === 第一步：初始化交易条件标志 ===

    # 初始化买卖条件标志
    buy_condition = False   # 买入条件
    sell_condition = False  # 卖出条件

    # 获取当前K线位置
    d = ContextInfo.barpos

    # === 第二步：SVM模型训练（仅在首次运行时执行） ===

    if ContextInfo.days == 0:

        print('开始SVM模型训练...')

        # --- 获取训练数据 ---

        # 获取2016年全年的历史数据作为训练集
        # 使用前复权数据确保价格连续性
        df = ContextInfo.get_market_data(['open','high','low','close','volume'],
                                       stock_code=[ContextInfo.stock],
                                       start_time='20160101',
                                       end_time='20170101',
                                       dividend_type='front')

        # 按时间排序确保数据顺序正确
        df = df.sort_index()

        # 提取交易日期和收盘价序列
        days = df.index.values          # 交易日期数组
        days_close = df['close'].values # 收盘价数组

        print('start training SVM')

        # --- 构建特征矩阵 ---

        # 初始化特征和标签列表
        x_all = []  # 特征矩阵
        y_all = []  # 标签向量

        # 滑动窗口构建训练样本
        # 从第15个交易日开始，到倒数第6个交易日结束
        # 确保每个样本都有15日历史数据和5日未来数据
        for i in range(14, len(days) - 5):

            # 获取当前样本的15日历史数据
            start_day = days[i - 14]  # 15日前的日期
            end_day = days[i]         # 当前日期

            # 获取15日的OHLCV数据
            data = ContextInfo.get_market_data(['open','high','low','close','volume'],
                                             stock_code=[ContextInfo.stock],
                                             end_time=end_day,
                                             count=15,
                                             skip_paused=False,
                                             dividend_type='front')
            data = data.sort_index()

            # 提取各项价格和成交量数据
            open = data['open'].values    # 开盘价序列
            close = data['close'].values  # 收盘价序列
            max = data['high'].values     # 最高价序列
            min = data['low'].values      # 最低价序列
            volume = data['volume'].values # 成交量序列

            # --- 特征工程：计算6个技术特征 ---

            # 特征1：相对收盘价（当前收盘价/15日平均收盘价）
            close_mean = close[-1] / np.mean(close)

            # 特征2：相对成交量（当前成交量/15日平均成交量）
            volume_mean = volume[-1] / np.mean(volume)

            # 特征3：相对最高价（当前最高价/15日平均最高价）
            max_mean = max[-1] / np.mean(max)

            # 特征4：相对最低价（当前最低价/15日平均最低价）
            min_mean = min[-1] / np.mean(min)

            # 特征5：当前成交量（绝对值）
            vol = volume[-1]

            # 特征6：15日总收益率（期末价格/期初价格）
            return_now = close[-1] / close[0]

            # 特征7：15日收盘价标准差（价格波动率）
            std = np.std(np.array(close), axis=0)

            # 组合所有特征为特征向量
            features = [close_mean, volume_mean, max_mean, min_mean, vol, return_now, std]
            x_all.append(features)

        # --- 构建标签向量 ---

        # 为每个样本生成标签（5日后的涨跌方向）
        for i in range(len(days_close) - 19):
            # 比较5日后价格与当前价格
            # 如果5日后价格更高，标签为1（上涨）
            # 否则标签为0（下跌）
            if days_close[i+19] > days_close[i+14]:
                label = 1  # 上涨
            else:
                label = 0  # 下跌
            y_all.append(label)

        # --- 准备训练数据 ---

        # 移除最后一个样本（因为没有对应的标签）
        x_train = x_all[:-1]  # 训练特征
        y_train = y_all[:-1]  # 训练标签

        # --- 创建和训练SVM模型 ---

        # 创建SVM分类器
        # 参数说明：
        # C=1.0: 正则化参数
        # kernel='rbf': 使用径向基函数核
        # gamma='auto': 自动设置核函数参数
        ContextInfo.clf = svm.SVC(C=1.0, kernel='rbf', degree=3, gamma='auto',
                                coef0=0.0, shrinking=True, probability=False,
                                tol=0.001, cache_size=200, verbose=False,
                                max_iter=-1, decision_function_shape='ovr',
                                random_state=None)

        # 训练模型
        try:
            ContextInfo.clf.fit(x_train, y_train)
            print('SVM模型训练完成！')
        except:
            # 捕获训练过程中的异常
            e = traceback.format_exc()
            print(('训练错误:', e))

        # 标记训练完成
        ContextInfo.days = 1

    # === 第三步：获取当前交易日的时间和数据信息 ===

    # 获取当前K线的时间戳
    timetag = ContextInfo.get_bar_timetag(d)

    # 获取过去15个交易日的时间范围
    timetag_start = ContextInfo.get_bar_timetag(d-15)  # 15日前的时间戳
    timetag_end = ContextInfo.get_bar_timetag(d-1)     # 前一日的时间戳

    # 将时间戳转换为可读的日期格式
    today = timetag_to_datetime(timetag, '%Y%m%d')           # 当前日期
    start_date = timetag_to_datetime(timetag_start, '%Y%m%d') # 开始日期
    end_date = timetag_to_datetime(timetag_end, '%Y%m%d')     # 结束日期

    # 获取当前是星期几（1=周一，2=周二，...，7=周日）
    weekday = datetime.strptime(today, '%Y%m%d').isoweekday()

    # 获取当前交易日的开盘价和收盘价
    open_today = ContextInfo.get_market_data(['open'], stock_code=[ContextInfo.stock],
                                           skip_paused=False, dividend_type='front')
    close_today = ContextInfo.get_market_data(['close'], stock_code=[ContextInfo.stock],
                                            skip_paused=False, dividend_type='front')

    # === 第四步：周一开仓逻辑（基于SVM预测） ===

    # 开仓条件：当前无持仓 且 当前是周一
    if ContextInfo.holding == 0 and weekday == 1:

        # --- 获取特征数据 ---

        # 获取过去15个交易日的OHLCV数据用于特征提取
        data = ContextInfo.get_market_data(['open','high','low','close','volume'],
                                         stock_code=[ContextInfo.stock],
                                         end_time=end_date,
                                         count=15,
                                         skip_paused=False,
                                         dividend_type='front')
        data = data.sort_index()

        # 提取各项价格和成交量数据
        close = data['close'].values   # 收盘价序列
        max = data['high'].values      # 最高价序列
        min = data['low'].values       # 最低价序列
        volume = data['volume'].values # 成交量序列

        # --- 计算特征向量（与训练时相同的特征） ---

        # 特征1：相对收盘价
        close_mean = close[-1] / np.mean(close)

        # 特征2：相对成交量
        volume_mean = volume[-1] / np.mean(volume)

        # 特征3：相对最高价
        max_mean = max[-1] / np.mean(max)

        # 特征4：相对最低价
        min_mean = min[-1] / np.mean(min)

        # 特征5：当前成交量
        vol = volume[-1]

        # 特征6：15日总收益率
        return_now = close[-1] / close[0]

        # 特征7：15日收盘价标准差
        std = np.std(np.array(close), axis=0)

        # --- 执行预测和交易决策 ---

        # 组合特征向量并调整为模型输入格式
        features = [close_mean, volume_mean, max_mean, min_mean, vol, return_now, std]
        features = np.array(features).reshape(1, -1)  # 转换为二维数组格式

        try:
            # 使用训练好的SVM模型进行预测
            prediction = ContextInfo.clf.predict(features)[0]

            # 如果预测结果为1（上涨），则开仓买入
            if prediction == 1:

                # 计算买入手数：使用95%的资金买入
                # 公式：(资金 × 0.95 / 开盘价) / 100，向下取整
                ContextInfo.holding = int(ContextInfo.money * 0.95 / open_today) / 100

                # 执行买入订单
                order_shares(ContextInfo.stock, ContextInfo.holding*100, 'fix',
                           open_today, ContextInfo, ContextInfo.accountid)

                # 记录买入价格，用于后续止盈止损计算
                ContextInfo.buyprice = open_today

                # 设置买入条件标志
                buy_condition = True

                # 输出交易信息
                print(today)
                print('open long position to 0.95')

        except:
            # 捕获预测过程中的异常
            print(('predict error occur,bar:', d))

    # === 第五步：止盈止损逻辑 ===

    # --- 止盈条件：收益率达到10% ---
    elif ContextInfo.holding > 0 and close_today / ContextInfo.buyprice >= 1.1:

        # 执行全部平仓
        order_shares(ContextInfo.stock, -ContextInfo.holding*100, 'fix',
                   close_today, ContextInfo, ContextInfo.accountid)

        # 清空持仓记录
        ContextInfo.holding = 0

        # 设置卖出条件标志
        sell_condition = True

        # 输出交易信息
        print(today)
        print('reach profit stop limit, close position')

    # --- 止损条件：周五且亏损超过2% ---
    elif (ContextInfo.holding > 0 and
          close_today / ContextInfo.buyprice < 0.98 and
          weekday == 5):

        # 执行全部平仓
        order_shares(ContextInfo.stock, -ContextInfo.holding*100, 'fix',
                   close_today, ContextInfo, ContextInfo.accountid)

        # 清空持仓记录
        ContextInfo.holding = 0

        # 设置卖出条件标志
        sell_condition = True

        # 输出交易信息
        print(today)
        print('reach lose stop limit, close position')

    # === 第六步：更新状态和绘制图表 ===

    # 更新交易日计数器
    ContextInfo.days += 1

    # 在图表上绘制买卖信号
    # 买入信号：1表示买入，0表示无操作
    ContextInfo.paint('do_buy', int(buy_condition), -1, 0)

    # 卖出信号：1表示卖出，0表示无操作
    ContextInfo.paint('do_sell', int(sell_condition), -1, 0)
























