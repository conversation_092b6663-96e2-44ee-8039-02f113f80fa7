#coding:gbk

"""
QMT交易实时主推示例
===================

本示例展示了QMT平台中5个核心交易主推函数的使用方法：
1. account_callback - 资金账号状态变化主推
2. order_callback - 委托状态变化主推
3. deal_callback - 成交信息主推
4. position_callback - 持仓变化主推
5. orderError_callback - 下单错误主推

这些回调函数会在相应的交易事件发生时自动被QMT系统调用，
实现实时监控交易状态的功能。

注意：
- accountInfo/orderInfo/dealInfo/positonInfo对象的属性与get_trade_detail_data返回的对应对象一致
- 详细参数说明请参考《python交易函数的详细参数说明》文档
"""


def init(ContextInfo):
    """
    策略初始化函数

    功能：设置策略运行的基本参数和账户信息

    参数：
        ContextInfo: QMT上下文对象，包含策略运行环境信息

    说明：
        - 设置交易账户为'*********'（示例账户）
        - 此函数在策略启动时执行一次
        - 可在此函数中进行账户设置、参数初始化等操作
    """
    # 设置交易账户ID，用于后续的交易操作
    # 注意：实际使用时需要替换为真实的账户ID
    ContextInfo.set_account('*********')

def handlebar(ContextInfo):
    """
    K线处理函数（空实现）

    功能：在每根K线完成时被调用，本示例中不执行任何操作

    参数：
        ContextInfo: QMT上下文对象

    说明：
        - 本示例主要演示交易主推功能，因此handlebar函数为空
        - 实际策略中可在此函数中实现交易逻辑
    """
    pass

# 资金账号主推函数
def account_callback(ContextInfo, accountInfo):
    """
    资金账号状态变化回调函数

    功能：当账户资金状态发生变化时自动调用

    参数：
        ContextInfo: QMT上下文对象
        accountInfo: 账户信息对象，包含以下主要属性：
            - m_strStatus: 账户状态（如：正常、冻结等）
            - m_dBalance: 总资产
            - m_dAvailable: 可用资金
            - m_dMarketValue: 总市值
            - m_dPositionProfit: 持仓盈亏

    触发时机：
        - 账户资金发生变化时
        - 持仓盈亏变化时
        - 账户状态改变时
    """
    print('accountInfo')  # 输出回调函数标识
    # 输出资金账号当前状态（如：正常、异常、冻结等）
    print(accountInfo.m_strStatus)

# 委托主推函数
def order_callback(ContextInfo, orderInfo):
    """
    委托状态变化回调函数

    功能：当委托单状态发生变化时自动调用

    参数：
        ContextInfo: QMT上下文对象
        orderInfo: 委托信息对象，包含以下主要属性：
            - m_strInstrumentID: 证券代码
            - m_strExchangeID: 交易所代码
            - m_nOffsetFlag: 买卖方向（23买入，24卖出）
            - m_nVolumeTotalOriginal: 委托数量
            - m_dLimitPrice: 委托价格
            - m_nOrderStatus: 委托状态

    触发时机：
        - 提交新委托时
        - 委托状态变化时（如：已报、部成、全成、已撤等）
        - 委托被拒绝时
    """
    print('orderInfo')  # 输出回调函数标识
    # 输出委托的证券代码（如：000001.SZ）
    print(orderInfo.m_strInstrumentID)

# 成交主推函数
def deal_callback(ContextInfo, dealInfo):
    """
    成交信息回调函数

    功能：当委托单发生成交时自动调用

    参数：
        ContextInfo: QMT上下文对象
        dealInfo: 成交信息对象，包含以下主要属性：
            - m_strInstrumentID: 证券代码
            - m_strExchangeID: 交易所代码
            - m_nOffsetFlag: 买卖方向
            - m_nVolume: 成交数量
            - m_dPrice: 成交价格
            - m_dTradeAmount: 成交金额
            - m_strTradeTime: 成交时间

    触发时机：
        - 委托单部分成交时
        - 委托单完全成交时
        - 每笔成交都会触发一次回调
    """
    print('dealInfo')  # 输出回调函数标识
    # 输出成交的证券代码（如：000001.SZ）
    print(dealInfo.m_strInstrumentID)

# 持仓主推函数
def position_callback(ContextInfo, positonInfo):
    """
    持仓变化回调函数

    功能：当账户持仓发生变化时自动调用

    参数：
        ContextInfo: QMT上下文对象
        positonInfo: 持仓信息对象，包含以下主要属性：
            - m_strInstrumentID: 证券代码
            - m_strExchangeID: 交易所代码
            - m_nVolume: 持仓数量
            - m_nCanUseVolume: 可用数量
            - m_dOpenPrice: 成本价
            - m_dPositionCost: 持仓成本
            - m_dPositionProfit: 持仓盈亏
            - m_dInstrumentValue: 持仓市值

    触发时机：
        - 买入股票增加持仓时
        - 卖出股票减少持仓时
        - 持仓盈亏发生变化时
        - 分红送股等公司行为影响持仓时
    """
    print('positonInfo')  # 输出回调函数标识
    # 输出持仓的证券代码（如：000001.SZ）
    print(positonInfo.m_strInstrumentID)

# 下单出错回调函数
def orderError_callback(ContextInfo, passOrderInfo, msg):
    """
    下单错误回调函数

    功能：当下单操作失败时自动调用

    参数：
        ContextInfo: QMT上下文对象
        passOrderInfo: 下单信息对象，包含以下主要属性：
            - orderCode: 下单代码
            - accountID: 账户ID
            - instrumentID: 证券代码
            - price: 委托价格
            - volume: 委托数量
        msg: 错误信息字符串，描述下单失败的具体原因

    触发时机：
        - 下单参数错误时
        - 账户权限不足时
        - 资金不足时
        - 持仓不足时
        - 交易时间不正确时
        - 其他下单失败情况

    用途：
        - 监控下单失败情况
        - 记录错误日志
        - 实现错误处理逻辑
        - 调试策略问题
    """
    print('orderError_callback')  # 输出回调函数标识
    # 输出下单操作的代码标识
    print(passOrderInfo.orderCode)
    # 输出具体的错误信息，帮助诊断下单失败原因
    print(msg)


