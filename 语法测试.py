# -*- coding: utf-8 -*-
"""
测试修复后的QMT兼容版文件语法
"""

def test_syntax():
    """测试文件语法是否正确"""
    try:
        print("🔍 开始语法测试...")
        
        # 读取并编译文件
        with open('框架/6sk线_QMT兼容版.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 编译检查语法
        compile(code, '框架/6sk线_QMT兼容版.py', 'exec')
        print("✅ 语法检查通过！")
        
        # 测试关键类是否可以创建
        exec(code)
        
        # 在全局命名空间中查找类
        if 'CompleteCMFBIASDivergenceDetector' in globals():
            detector = CompleteCMFBIASDivergenceDetector()
            print("✅ 检测器类创建成功！")
        else:
            print("⚠️ 检测器类未找到")
            
        print("🎉 所有测试通过！文件已修复完成")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_syntax()
