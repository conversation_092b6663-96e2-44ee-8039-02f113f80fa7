#encoding:gbk
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于计算均线等数学运算
import datetime      # 日期时间处理库，用于获取当前时间和时间判断

"""
双均线实盘交易策略
==================

策略原理：
- 基于快速移动平均线和慢速移动平均线的交叉信号进行交易
- 金叉（快线上穿慢线）时买入股票
- 死叉（快线下穿慢线）时卖出股票

技术指标：
- 快线：17日移动平均线
- 慢线：27日移动平均线

交易逻辑：
1. 当快线从下方穿越慢线时（金叉），产生买入信号
2. 当快线从上方穿越慢线时（死叉），产生卖出信号
3. 每次买入固定金额（10000元）
4. 卖出时清空全部持仓

风控措施：
- 检查账户资金是否充足
- 防止重复下单（等待委托确认）
- 仅在交易时间内执行
- 跳过历史K线，仅处理实时数据

注意事项：
- 适用于实盘交易环境
- 需要配置真实的交易账户
- 建议在模拟环境中充分测试后再用于实盘
"""

class a():
    """
    全局状态保存类

    说明：
    - 由于ContextInfo对象在每次handlebar调用前会被深拷贝
    - 如果调用handlebar的分笔不是K线最后分笔，ContextInfo会被回退
    - 因此不能用ContextInfo来记录快速交易的信号和状态
    - 使用独立的类实例来保存策略状态和委托信息
    """
    pass

# 创建全局状态实例，用于保存委托状态和策略参数
A = a()

def init(C):
    """
    策略初始化函数

    功能：设置策略参数、账户信息和交易配置

    参数：
        C: ContextInfo对象，包含策略运行环境信息

    全局变量设置：
        A.stock: 交易标的证券代码
        A.acct: 交易账户ID
        A.acct_type: 账户类型（STOCK普通账户/CREDIT信用账户）
        A.amount: 单笔买入金额（元）
        A.line1: 快线周期（日）
        A.line2: 慢线周期（日）
        A.waiting_list: 待确认委托列表
        A.buy_code/A.sell_code: 买卖操作代码
    """
    # 构建完整的证券代码（代码.市场），如：000001.SZ
    A.stock = C.stockcode + '.' + C.market

    # 获取交易账户信息（从模型交易界面选择的账号）
    A.acct = account          # 账户ID
    A.acct_type = accountType # 账户类型：STOCK(普通账户) 或 CREDIT(信用账户)

    # 交易参数设置
    A.amount = 10000  # 单笔买入金额，单位：元
    A.line1 = 17      # 快速移动平均线周期，单位：交易日
    A.line2 = 27      # 慢速移动平均线周期，单位：交易日

    # 委托管理
    A.waiting_list = []  # 未确认委托列表，防止重复下单

    # 根据账户类型设置买卖操作代码
    # 普通账户：23买入，24卖出
    # 信用账户：33买入，34卖出
    A.buy_code = 23 if A.acct_type == 'STOCK' else 33
    A.sell_code = 24 if A.acct_type == 'STOCK' else 34

    # 设置股票池，订阅目标品种的行情数据
    C.set_universe([A.stock])

    # 输出策略初始化信息
    print(f'双均线实盘示例{A.stock} {A.acct} {A.acct_type} 单笔买入金额{A.amount}')

def handlebar(C):
    """
    K线数据处理函数（策略核心逻辑）

    功能：在每根K线完成时执行双均线交易策略

    参数：
        C: ContextInfo对象，包含当前K线和市场数据

    执行流程：
    1. 数据有效性检查（跳过历史K线、检查交易时间）
    2. 账户状态检查（登录状态、可用资金）
    3. 委托状态管理（检查待确认委托）
    4. 持仓信息获取
    5. 历史价格数据获取和均线计算
    6. 交易信号判断和执行

    交易条件：
    - 买入：资金充足 + 无持仓 + 快线上穿慢线（金叉）
    - 卖出：有持仓 + 快线下穿慢线（死叉）
    """

    # === 第一步：数据有效性检查 ===

    # 跳过历史K线，仅处理最新的实时K线
    # 避免在历史数据上执行交易操作
    if not C.is_last_bar():
        return

    # 获取当前时间并格式化为HHMMSS格式
    now = datetime.datetime.now()
    now_time = now.strftime('%H%M%S')

    # 跳过非交易时间，仅在09:30:00-15:00:00之间执行策略
    # 避免在集合竞价、午休、收盘后执行交易
    if now_time < '093000' or now_time > "150000":
        return

    # === 第二步：账户状态检查 ===

    # 获取账户信息，检查账户登录状态
    account = get_trade_detail_data(A.acct, A.acct_type, 'account')
    if len(account) == 0:
        print(f'账号{A.acct} 未登录 请检查')
        return

    # 提取账户信息并获取可用资金
    account = account[0]
    available_cash = int(account.m_dAvailable)  # 可用资金，单位：元

    # === 第三步：委托状态管理 ===

    # 检查是否有待确认的委托，防止重复下单
    if A.waiting_list:
        found_list = []  # 已确认的委托列表

        # 查询当前所有委托订单
        orders = get_trade_detail_data(A.acct, A.acct_type, 'order')

        # 检查待确认委托是否已经在系统中找到
        for order in orders:
            if order.m_strRemark in A.waiting_list:
                found_list.append(order.m_strRemark)

        # 从待确认列表中移除已找到的委托
        A.waiting_list = [i for i in A.waiting_list if i not in found_list]

    # 如果仍有未确认的委托，暂停新的交易操作
    if A.waiting_list:
        print(f"当前有未查到委托 {A.waiting_list} 暂停后续报单")
        return

    # === 第四步：持仓信息获取 ===

    # 获取当前持仓信息
    holdings = get_trade_detail_data(A.acct, A.acct_type, 'position')

    # 将持仓信息转换为字典格式：{证券代码: 可用数量}
    holdings = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nCanUseVolume for i in holdings}

    # === 第五步：历史数据获取和均线计算 ===

    # 获取历史收盘价数据，数量为最大均线周期+1
    # 使用前复权数据确保价格连续性
    data = C.get_history_data(max(A.line1, A.line2)+1, '1d', 'close', dividend_type='front_ratio')
    close_list = data[A.stock]  # 提取目标股票的收盘价序列

    # 检查数据长度是否足够计算均线
    if len(close_list) < max(A.line1, A.line2)+1:
        print('行情长度不足(新上市或最近有停牌) 跳过运行')
        return

    # 计算前一日的快慢均线值（用于判断穿越方向）
    pre_line1 = np.mean(close_list[-A.line1-1: -1])    # 前一日快线值
    pre_line2 = np.mean(close_list[-A.line2-1: -1])    # 前一日慢线值

    # 计算当前的快慢均线值
    current_line1 = np.mean(close_list[-A.line1:])     # 当前快线值
    current_line2 = np.mean(close_list[-A.line2:])     # 当前慢线值

    # === 第六步：交易信号判断和执行 ===

    # 计算买入数量：根据固定金额和当前价格计算，向下取整到100股的整数倍
    vol = int(A.amount / close_list[-1] / 100) * 100

    # 买入条件判断：金叉信号 + 资金充足 + 无持仓 + 数量有效
    if (A.amount < available_cash and           # 可用资金充足
        vol >= 100 and                         # 买入数量至少100股
        A.stock not in holdings and            # 当前无持仓
        pre_line1 < pre_line2 and              # 前一日快线在慢线下方
        current_line1 > current_line2):        # 当前快线在慢线上方（金叉）

        # 执行买入操作
        msg = f"双均线实盘 {A.stock} 上穿均线 买入 {vol}股"

        # 下单参数说明：
        # A.buy_code: 买入操作代码
        # 1101: 委托类型（限价委托）
        # A.acct: 交易账户
        # A.stock: 证券代码
        # 14: 价格类型（对手价）
        # -1: 价格（-1表示使用价格类型）
        # vol: 委托数量
        # '双均线实盘': 策略名称
        # 1: quickTrade参数（立即下单）
        # msg: 委托备注
        # C: ContextInfo对象
        passorder(A.buy_code, 1101, A.acct, A.stock, 14, -1, vol, '双均线实盘', 1, msg, C)
        print(msg)
        A.waiting_list.append(msg)  # 将委托加入待确认列表

    # 卖出条件判断：死叉信号 + 有持仓
    if (A.stock in holdings and                # 当前有持仓
        holdings[A.stock] > 0 and              # 持仓数量大于0
        pre_line1 > pre_line2 and              # 前一日快线在慢线上方
        current_line1 < current_line2):        # 当前快线在慢线下方（死叉）

        # 执行卖出操作（全部清仓）
        msg = f"双均线实盘 {A.stock} 下穿均线 卖出 {holdings[A.stock]}股"

        # 卖出全部可用持仓
        passorder(A.sell_code, 1101, A.acct, A.stock, 14, -1, holdings[A.stock], '双均线实盘', 1, msg, C)
        print(msg)
        A.waiting_list.append(msg)  # 将委托加入待确认列表
