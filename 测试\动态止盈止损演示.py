#coding:gbk

"""
动态止盈止损逻辑演示
展示基于实际市场波动的动态止盈止损计算原理
"""

def calculate_dynamic_stop_profit_loss(atr_value, current_price, volatility_ratio):
    """
    计算基于市场波动的动态止盈止损
    
    参数:
        atr_value: 当前ATR值
        current_price: 当前价格
        volatility_ratio: 波动率比值 (当前ATR / ATR均值)
    
    返回:
        dict: 动态止盈止损信息
    """
    # 计算实际波动幅度 = ATR / 当前价格 × 100%
    actual_volatility = (atr_value / current_price * 100) if current_price > 0 and atr_value > 0 else 1.5
    
    # 根据波动率比值确定系数
    if volatility_ratio <= 0.8:
        volatility_zone = '低波动区'
        take_profit_coeff = 0.7  # 捕获70%的波动作为止盈
        stop_loss_coeff = 0.4    # 40%的波动作为止损
    elif volatility_ratio > 0.8 and volatility_ratio <= 1.2:
        volatility_zone = '正常波动区'
        take_profit_coeff = 0.8
        stop_loss_coeff = 0.5
    elif volatility_ratio > 1.2 and volatility_ratio <= 1.8:
        volatility_zone = '高波动区'
        take_profit_coeff = 0.9
        stop_loss_coeff = 0.6
    else:  # volatility_ratio > 1.8
        volatility_zone = '极高波动区'
        take_profit_coeff = 1.0
        stop_loss_coeff = 0.7
    
    # 计算动态止盈止损比例
    dynamic_take_profit = actual_volatility * take_profit_coeff
    dynamic_stop_loss = actual_volatility * stop_loss_coeff
    
    # 合理性检查
    dynamic_take_profit = max(0.3, min(dynamic_take_profit, 8.0))
    dynamic_stop_loss = max(0.2, min(dynamic_stop_loss, 5.0))
    
    # 确保止盈 > 止损
    if dynamic_take_profit <= dynamic_stop_loss:
        dynamic_take_profit = dynamic_stop_loss * 1.5
    
    return {
        'volatility_zone': volatility_zone,
        'actual_volatility': actual_volatility,
        'dynamic_take_profit': dynamic_take_profit,
        'dynamic_stop_loss': dynamic_stop_loss,
        'take_profit_coeff': take_profit_coeff,
        'stop_loss_coeff': stop_loss_coeff
    }

def demo_scenarios():
    """演示不同市场场景"""
    print("="*80)
    print("🧪 基于市场波动的动态止盈止损逻辑演示")
    print("="*80)
    
    scenarios = [
        {
            'name': '低波动震荡市',
            'atr': 0.05,      # ATR = 0.05元
            'price': 10.0,    # 价格 = 10元
            'ratio': 0.7,     # 波动率比值 = 0.7
            'description': '市场波动小，适合短线操作'
        },
        {
            'name': '正常波动市场',
            'atr': 0.15,      # ATR = 0.15元
            'price': 10.0,    # 价格 = 10元
            'ratio': 1.0,     # 波动率比值 = 1.0
            'description': '市场波动正常，标准操作'
        },
        {
            'name': '高波动趋势市',
            'atr': 0.30,      # ATR = 0.30元
            'price': 10.0,    # 价格 = 10元
            'ratio': 1.5,     # 波动率比值 = 1.5
            'description': '市场波动较大，需要放宽止损'
        },
        {
            'name': '极高波动市场',
            'atr': 0.50,      # ATR = 0.50元
            'price': 10.0,    # 价格 = 10元
            'ratio': 2.0,     # 波动率比值 = 2.0
            'description': '市场剧烈波动，大幅调整止盈止损'
        }
    ]
    
    print(f"{'场景':<12} {'波动幅度':<8} {'止盈目标':<8} {'止损目标':<8} {'风险收益比':<10} {'说明'}")
    print("-" * 80)
    
    for scenario in scenarios:
        result = calculate_dynamic_stop_profit_loss(
            scenario['atr'], 
            scenario['price'], 
            scenario['ratio']
        )
        
        risk_reward_ratio = result['dynamic_take_profit'] / result['dynamic_stop_loss']
        
        print(f"{scenario['name']:<12} "
              f"{result['actual_volatility']:.2f}%     "
              f"{result['dynamic_take_profit']:.2f}%    "
              f"{result['dynamic_stop_loss']:.2f}%    "
              f"{risk_reward_ratio:.1f}:1       "
              f"{scenario['description']}")
    
    print("\n" + "="*80)
    print("💡 新逻辑优势分析:")
    print("="*80)
    
    print("1. 📊 适应性强：")
    print("   - 低波动时：目标现实，容易达到盈利")
    print("   - 高波动时：充分利用市场机会，避免过早离场")
    
    print("\n2. 🛡️ 风险控制：")
    print("   - 动态止损：高波动时放宽止损，避免被正常回调震出")
    print("   - 合理比例：始终保持止盈目标 > 止损目标")
    
    print("\n3. 📈 收益优化：")
    print("   - 低波动：1.75:1 风险收益比，提高胜率")
    print("   - 高波动：1.5:1 风险收益比，平衡风险与收益")
    
    print("\n4. 🎯 实战意义：")
    print("   - 解决了固定止盈止损与市场波动不匹配的问题")
    print("   - 提高了策略在不同市场环境下的适应性")
    print("   - 基于ATR的科学计算，更贴近市场实际情况")

def compare_with_old_logic():
    """与旧逻辑对比"""
    print("\n" + "="*80)
    print("📋 新旧逻辑对比分析")
    print("="*80)
    
    # 旧逻辑：固定的止盈止损比例
    old_take_profit = 1.5  # 固定1.5%
    old_stop_loss = 1.5    # 固定1.5%
    
    print("旧逻辑问题:")
    print("- 低波动市场(0.5%波动)：1.5%止盈目标过高，难以达到")
    print("- 高波动市场(3.0%波动)：1.5%止盈目标过低，错失机会")
    print("- 止损固定：无法根据市场波动调整，容易被震出或风险过大")
    
    print("\n新逻辑改进:")
    print("- 低波动市场：0.35%止盈，0.20%止损 → 现实可达")
    print("- 高波动市场：2.70%止盈，1.80%止损 → 充分利用波动")
    print("- 动态调整：根据实际市场波动科学设置目标")
    
    print("\n实际效果预期:")
    print("✅ 提高低波动市场的盈利频率")
    print("✅ 增加高波动市场的单次收益")
    print("✅ 减少因不合理止损被震出的情况")
    print("✅ 整体提升策略的风险调整收益")

if __name__ == "__main__":
    demo_scenarios()
    compare_with_old_logic()
    
    print("\n" + "="*80)
    print("✅ 动态止盈止损逻辑演示完成")
    print("💡 建议：实盘使用前根据具体标的特征微调系数")
    print("="*80)
