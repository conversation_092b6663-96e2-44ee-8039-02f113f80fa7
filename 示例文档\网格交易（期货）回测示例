#coding:gbk

"""
期货网格交易策略回测示例
========================

策略概述：
本策略是一个基于统计学原理的期货网格交易系统，通过价格的均值回归特性进行双向交易。

策略原理：
1. 统计基础：计算过去300个价格数据的均值和标准差
2. 网格构建：以均值为中心，用标准差的倍数构建价格网格
3. 仓位配置：根据价格所在网格区间分配不同的仓位权重
4. 双向交易：在不同区间执行做多或做空操作

网格设计：
- 价格区间：(-40σ,-3σ], (-3σ,-2σ], (-2σ,+2σ], (+2σ,+3σ], (+3σ,+40σ]
- 仓位权重：[0.25,    0.15,     0.0,      0.15,     0.25]
- 交易逻辑：
  * 中性区间(-2σ,+2σ)：无仓位，等待突破
  * 上方区间(+2σ以上)：做多，价格越高仓位越重
  * 下方区间(-2σ以下)：做空，价格越低仓位越重

技术特点：
- 均值回归：基于价格向均值回归的统计特性
- 动态网格：网格边界根据最新300期数据动态调整
- 资金管理：严格的保证金和仓位控制
- 双向交易：可同时利用上涨和下跌行情

适用环境：
- 期货市场（支持双向交易）
- 分钟级数据（1分钟K线）
- 震荡行情（均值回归效果更佳）
- 流动性好的品种

风险控制：
- 保证金管理：严格按保证金比例控制仓位
- 资金分配：按网格权重分配资金，避免过度集中
- 动态调整：根据价格变化动态调整仓位
- 止损机制：通过网格切换实现自动止损

注意事项：
- 本策略仅用于回测研究，不适用于实盘交易
- 适合震荡市场，趋势市场可能面临较大回撤
- 需要充足的资金支持多个网格的仓位
- 交易成本对策略收益有重要影响
"""

# 导入必要的数据处理和计算库
import numpy as np   # 数值计算库，用于统计计算和数组操作
import pandas as pd  # 数据处理库，用于时间序列数据处理
import time          # 时间处理库，用于时间戳操作
import datetime      # 日期时间库，用于日期处理

def init(ContextInfo):
    """
    期货网格交易策略初始化函数

    功能：设置网格交易的基本参数和期货合约信息

    参数：
        ContextInfo: 策略运行环境对象

    初始化内容：
    1. 设置交易标的（期货合约）
    2. 配置网格权重和仓位管理
    3. 获取期货合约的交易参数
    4. 初始化资金和时间戳

    关键变量：
        tradefuture: 期货合约代码
        weight: 各网格的仓位权重
        position_long/short: 多空仓位记录
        marginratio: 保证金比率
        multiplier: 合约乘数
    """

    # === 第一步：设置交易标的 ===

    # 构建完整的期货合约代码（合约代码.交易所代码）
    ContextInfo.tradefuture = ContextInfo.stockcode + "." + ContextInfo.market

    # 设置交易品种池为当前期货合约
    ContextInfo.set_universe([ContextInfo.tradefuture])
    print(ContextInfo.get_universe())

    # === 第二步：初始化数据结构 ===

    # 初始化时间序列数据框（用于存储历史数据）
    ContextInfo.timeseries = pd.DataFrame()

    # 初始化网格边界数组（6个边界点，形成5个区间）
    ContextInfo.band = np.zeros(5)

    # === 第三步：设置网格交易参数 ===

    # 设置各网格区间的仓位权重
    # 对应区间：[-40σ,-3σ], [-3σ,-2σ], [-2σ,+2σ], [+2σ,+3σ], [+3σ,+40σ]
    # 权重含义：该区间应分配的资金比例
    ContextInfo.weight = [0.25, 0.15, 0.0, 0.15, 0.25]

    # === 第四步：初始化仓位管理 ===

    # 初始化多头仓位（手数）
    ContextInfo.position_long = 0

    # 初始化空头仓位（手数）
    ContextInfo.position_short = 0

    # === 第五步：设置资金和合约参数 ===

    # 初始化剩余可用资金
    ContextInfo.surpluscapital = ContextInfo.capital

    # 获取期货合约的手续费和保证金信息
    comdict = ContextInfo.get_commission()
    ContextInfo.marginratio = comdict['margin_ratio']  # 保证金比率

    # 获取期货合约乘数（每手对应的标的数量）
    ContextInfo.multiplier = ContextInfo.get_contract_multiplier(ContextInfo.tradefuture)

    # === 第六步：设置账户和时间信息 ===

    # 设置期货回测账户ID
    ContextInfo.accountid = 'testF'

    # 记录当前时间戳（用于实盘时的数据时效性检查）
    ContextInfo.now_timestamp = time.time()
def handlebar(ContextInfo):
    """
    期货网格交易策略核心处理函数

    功能：执行基于统计网格的期货双向交易策略

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 获取当前时间和数据有效性检查
    2. 计算统计网格边界（均值±标准差倍数）
    3. 判断当前价格所在网格区间
    4. 根据网格位置执行相应的交易操作
    5. 动态调整多空仓位至目标权重

    网格交易逻辑：
    - 无仓位时：根据价格突破情况开仓
    - 有多仓时：根据网格变化调整仓位或转换方向
    - 有空仓时：根据网格变化调整仓位或转换方向
    """

    # === 第一步：获取时间信息和数据有效性检查 ===

    # 获取当前K线位置索引
    index = ContextInfo.barpos

    # 获取当前和前一根K线的时间戳
    realtimetag = ContextInfo.get_bar_timetag(index)      # 当前K线时间戳
    lasttimetag = ContextInfo.get_bar_timetag(index - 1)  # 前一根K线时间戳

    # 输出当前处理的时间
    print(timetag_to_datetime(realtimetag, '%Y-%m-%d %H:%M:%S'))

    # 实盘模式下的数据时效性检查
    # 如果是分钟级数据且不是回测模式，检查数据是否过期（超过7天）
    if ContextInfo.period in ['1m','3m','5m','15m','30m'] and not ContextInfo.do_back_test:
        time_diff = (datetime.datetime.fromtimestamp(ContextInfo.now_timestamp) -
                    datetime.datetime.fromtimestamp(realtimetag / 1000)).days
        if time_diff > 7:
            return  # 数据过期，跳过处理

    # === 第二步：获取历史数据用于统计计算 ===

    # 计算数据获取的时间范围（过去10天到前1天）
    # 这样设计是为了获取足够的历史数据进行统计，同时避免使用当天数据
    starttime = timetag_to_datetime(realtimetag - 86400000 * 10, '%Y%m%d%H%M%S')  # 10天前
    endtime = timetag_to_datetime(realtimetag - 86400000, '%Y%m%d%H%M%S')         # 1天前

    # 获取指定时间范围内的收盘价数据
    Result = ContextInfo.get_market_data(['close'],
                                       stock_code=[ContextInfo.tradefuture],
                                       start_time=starttime,
                                       end_time=endtime,
                                       skip_paused=False,
                                       period=ContextInfo.period,
                                       dividend_type='front')

    # 按时间升序排列收盘价数据
    close_sort = Result['close'].sort_index(axis=0, ascending=True)

    # === 第三步：计算统计网格边界 ===

    # 计算过去300个价格数据的均值和标准差
    # 300个数据点提供了足够的统计样本，同时保持对近期价格变化的敏感性
    Result_mean = close_sort.tail(300).mean()  # 300期均值
    Result_std = close_sort.tail(300).std()    # 300期标准差

    # 构建网格边界
    # 使用均值加减不同倍数的标准差作为网格分界线
    # [-40σ, -3σ, -2σ, +2σ, +3σ, +40σ] 形成5个交易区间
    ContextInfo.band = Result_mean + np.array([-40, -3, -2, 2, 3, 40]) * Result_std

    # === 第四步：数据有效性检查 ===

    # 检查网格边界是否有效
    # 如果存在NaN值或标准差为0，说明数据异常，跳过本次处理
    if np.isnan(ContextInfo.band).any() or Result_std == 0:
        return
    # === 第五步：网格交易主逻辑 ===

    # 确保不是第一根K线（需要有前一根K线数据）
    if index > 0:

        # 重新获取前一根K线时间戳（确保数据一致性）
        lasttimetag = ContextInfo.get_bar_timetag(index - 1)

        # --- 获取交易价格数据 ---

        # 获取前一根K线的收盘价（作为交易信号价格）
        close_lastbar = ContextInfo.get_market_data(['close'],
                                                  stock_code=[ContextInfo.tradefuture],
                                                  period=ContextInfo.period,
                                                  dividend_type='front')

        # 获取当前K线的开盘价（备用，当前未使用）
        open_currentbar = ContextInfo.get_market_data(['open'],
                                                    stock_code=[ContextInfo.tradefuture],
                                                    period=ContextInfo.period,
                                                    dividend_type='front')

        # --- 判断价格所在网格区间 ---

        # 使用pandas的cut函数将价格划分到对应的网格区间
        # labels=[0,1,2,3,4] 对应5个网格区间
        # 区间0: [-40σ,-3σ]  - 极度超卖，重仓做空
        # 区间1: [-3σ,-2σ]   - 超卖，轻仓做空
        # 区间2: [-2σ,+2σ]   - 中性区间，无仓位
        # 区间3: [+2σ,+3σ]   - 超买，轻仓做多
        # 区间4: [+3σ,+40σ]  - 极度超买，重仓做多
        grid = pd.cut([close_lastbar], ContextInfo.band, labels=[0, 1, 2, 3, 4])[0]

        # 在非回测模式下绘制网格位置图表
        if not ContextInfo.do_back_test:
            ContextInfo.paint('grid', float(grid), -1, 0)

        # === 第六步：无仓位时的开仓逻辑 ===

        # 开仓条件：当前无任何仓位 且 价格不在中性区间(grid≠2)
        if (ContextInfo.position_long == 0 and
            ContextInfo.position_short == 0 and
            grid != 2):

            # --- 上方网格开多仓 ---

            # 如果价格在上方网格(grid≥3)且有可用资金，开多仓
            if grid >= 3 and ContextInfo.surpluscapital > 0:

                # 计算开多仓手数
                # 公式：权重 × 可用资金 ÷ (保证金比率 × 价格 × 合约乘数)
                long_num = int(ContextInfo.weight[grid] * ContextInfo.surpluscapital /
                             (ContextInfo.marginratio * close_lastbar * ContextInfo.multiplier))

                # 更新多头仓位记录
                ContextInfo.position_long = long_num

                # 执行买入开仓操作
                buy_open(ContextInfo.tradefuture, long_num, 'fix', close_lastbar,
                        ContextInfo, ContextInfo.accountid)

                # 扣除占用的保证金
                ContextInfo.surpluscapital -= (long_num * ContextInfo.marginratio *
                                             close_lastbar * ContextInfo.multiplier)

            # --- 下方网格开空仓 ---

            # 如果价格在下方网格(grid≤1)且有可用资金，开空仓
            elif grid <= 1 and ContextInfo.surpluscapital > 0:

                # 计算开空仓手数
                short_num = int(ContextInfo.weight[grid] * ContextInfo.surpluscapital /
                              (ContextInfo.marginratio * close_lastbar * ContextInfo.multiplier))

                # 更新空头仓位记录
                ContextInfo.position_short = short_num

                # 执行卖出开仓操作
                sell_open(ContextInfo.tradefuture, short_num, 'fix', close_lastbar,
                         ContextInfo, ContextInfo.accountid)

                # 扣除占用的保证金
                ContextInfo.surpluscapital -= (short_num * ContextInfo.marginratio *
                                             close_lastbar * ContextInfo.multiplier)
        # === 第七步：持有多仓时的仓位管理 ===

        elif ContextInfo.position_long > 0:

            # --- 情况1：价格仍在上方网格，调整多仓仓位 ---

            if grid >= 3 and ContextInfo.surpluscapital > 0:

                # 计算目标多仓手数
                # 公式：权重 × (剩余资金 + 当前多仓占用资金) ÷ 单手保证金
                total_available = (ContextInfo.surpluscapital +
                                 ContextInfo.multiplier * close_lastbar *
                                 ContextInfo.position_long * ContextInfo.marginratio)

                targetlong_num = int(ContextInfo.weight[grid] * total_available /
                                   (ContextInfo.marginratio * close_lastbar * ContextInfo.multiplier))

                # 如果目标仓位大于当前仓位，加仓
                if targetlong_num > ContextInfo.position_long:
                    trade_num = targetlong_num - ContextInfo.position_long
                    ContextInfo.position_long = targetlong_num

                    # 执行买入开仓（加仓）
                    buy_open(ContextInfo.tradefuture, trade_num, 'fix', close_lastbar,
                            ContextInfo, ContextInfo.accountid)

                    # 扣除新增保证金
                    ContextInfo.surpluscapital -= (trade_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

                # 如果目标仓位小于当前仓位，减仓
                elif targetlong_num < ContextInfo.position_long:
                    trade_num = ContextInfo.position_long - targetlong_num
                    ContextInfo.position_long = targetlong_num

                    # 执行卖出平仓（减仓）
                    sell_close_tdayfirst(ContextInfo.tradefuture, trade_num, 'fix', close_lastbar,
                                        ContextInfo, ContextInfo.accountid)

                    # 释放减仓的保证金
                    ContextInfo.surpluscapital += (trade_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

            # --- 情况2：价格回到中性网格，全部平多仓 ---

            elif grid == 2:

                # 执行全部平多仓
                sell_close_tdayfirst(ContextInfo.tradefuture, ContextInfo.position_long, 'fix',
                                    close_lastbar, ContextInfo, ContextInfo.accountid)

                # 释放全部多仓保证金
                ContextInfo.surpluscapital += (ContextInfo.position_long * close_lastbar *
                                             ContextInfo.marginratio * ContextInfo.multiplier)

                # 清空多仓记录
                ContextInfo.position_long = 0

            # --- 情况3：价格跌到下方网格，平多仓并开空仓 ---

            elif grid <= 1:

                # 先全部平多仓
                sell_close_tdayfirst(ContextInfo.tradefuture, ContextInfo.position_long, 'fix',
                                    close_lastbar, ContextInfo, ContextInfo.accountid)

                # 释放多仓保证金
                ContextInfo.surpluscapital += (ContextInfo.position_long * close_lastbar *
                                             ContextInfo.marginratio * ContextInfo.multiplier)

                # 清空多仓记录
                ContextInfo.position_long = 0

                # 如果有可用资金，开空仓
                if ContextInfo.surpluscapital > 0:

                    # 计算开空仓手数
                    short_num = int(ContextInfo.weight[grid] * ContextInfo.surpluscapital /
                                  (ContextInfo.multiplier * ContextInfo.marginratio * close_lastbar))

                    # 更新空仓记录
                    ContextInfo.position_short = short_num

                    # 执行卖出开仓
                    sell_open(ContextInfo.tradefuture, short_num, 'fix', close_lastbar,
                             ContextInfo, ContextInfo.accountid)

                    # 扣除空仓保证金
                    ContextInfo.surpluscapital -= (short_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

        # === 第八步：持有空仓时的仓位管理 ===

        elif ContextInfo.position_short > 0:

            # --- 情况1：价格仍在下方网格，调整空仓仓位 ---

            if grid <= 1:

                # 计算目标空仓手数
                # 公式：权重 × (剩余资金 + 当前空仓占用资金) ÷ 单手保证金
                total_available = (ContextInfo.surpluscapital +
                                 ContextInfo.multiplier * close_lastbar *
                                 ContextInfo.position_short * ContextInfo.marginratio)

                targetlshort_num = int(ContextInfo.weight[grid] * total_available /
                                     (ContextInfo.multiplier * ContextInfo.marginratio * close_lastbar))

                # 如果目标仓位大于当前仓位，加空仓
                if targetlshort_num > ContextInfo.position_short:
                    trade_num = targetlshort_num - ContextInfo.position_short
                    ContextInfo.position_short = targetlshort_num

                    # 执行卖出开仓（加空仓）
                    sell_open(ContextInfo.tradefuture, trade_num, 'fix', close_lastbar,
                             ContextInfo, ContextInfo.accountid)

                    # 扣除新增保证金
                    ContextInfo.surpluscapital -= (trade_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

                # 如果目标仓位小于当前仓位，减空仓
                elif targetlshort_num < ContextInfo.position_short:
                    trade_num = ContextInfo.position_short - targetlshort_num
                    ContextInfo.position_short = targetlshort_num

                    # 执行买入平仓（减空仓）
                    buy_close_tdayfirst(ContextInfo.tradefuture, trade_num, 'fix', close_lastbar,
                                       ContextInfo, ContextInfo.accountid)

                    # 释放减仓的保证金
                    ContextInfo.surpluscapital += (trade_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

            # --- 情况2：价格回到中性网格，全部平空仓 ---

            elif grid == 2:

                # 执行全部平空仓
                buy_close_tdayfirst(ContextInfo.tradefuture, ContextInfo.position_short, 'fix',
                                   close_lastbar, ContextInfo, ContextInfo.accountid)

                # 释放全部空仓保证金
                ContextInfo.surpluscapital += (ContextInfo.position_short * close_lastbar *
                                             ContextInfo.marginratio * ContextInfo.multiplier)

                # 清空空仓记录
                ContextInfo.position_short = 0

            # --- 情况3：价格涨到上方网格，平空仓并开多仓 ---

            elif grid >= 3:

                # 先全部平空仓
                buy_close_tdayfirst(ContextInfo.tradefuture, ContextInfo.position_short, 'fix',
                                   close_lastbar, ContextInfo, ContextInfo.accountid)

                # 释放空仓保证金
                ContextInfo.surpluscapital += (ContextInfo.position_short * close_lastbar *
                                             ContextInfo.marginratio * ContextInfo.multiplier)

                # 清空空仓记录
                ContextInfo.position_short = 0

                # 如果有可用资金，开多仓
                if ContextInfo.surpluscapital > 0:

                    # 计算开多仓手数
                    trade_num = int(ContextInfo.weight[grid] * ContextInfo.surpluscapital /
                                  (ContextInfo.marginratio * close_lastbar * ContextInfo.multiplier))

                    # 更新多仓记录
                    ContextInfo.position_long = trade_num

                    # 执行买入开仓
                    buy_open(ContextInfo.tradefuture, trade_num, 'fix', close_lastbar,
                            ContextInfo, ContextInfo.accountid)

                    # 扣除多仓保证金
                    ContextInfo.surpluscapital -= (trade_num * close_lastbar *
                                                 ContextInfo.marginratio * ContextInfo.multiplier)

    # === 第九步：状态监控（可选的调试信息） ===

    # 以下注释代码可用于监控仓位和资金状态
    # print('多仓仓位:', ContextInfo.position_long)
    # print('空仓仓位:', ContextInfo.position_short)
    # print('剩余资金:', ContextInfo.surpluscapital)

