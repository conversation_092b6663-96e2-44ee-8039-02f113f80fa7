# coding:gbk

"""
日内回转交易策略回测示例
========================

策略概述：
本策略是一个基于MACD指标的日内回转交易系统，适用于T+0交易机制的市场或融券做空的场景。

策略原理：
1. 基础持仓：首先建立100手（10000股）的基础持仓
2. 日内交易：根据MACD信号进行小额度的买卖操作
3. 仓位回转：收盘前将仓位调整回基础持仓水平

技术指标：
- MACD(12,26,9)：基于60秒数据计算的MACD指标
- 信号周期：使用35个数据点计算MACD

交易逻辑：
- 买入信号：MACD从负转正（MACD>0 且 MACD_pre<0）
- 卖出信号：MACD从正转负（MACD<0 且 MACD_pre>0）
- 交易单位：每次100股（1手）
- 时间限制：14:55后停止交易

风控措施：
1. 仓位限制：日内操作不超过基础仓位
2. 时间控制：收盘前强制回转至基础仓位
3. 资金检查：确保有足够资金执行交易
4. 持仓验证：确保有足够持仓执行卖出

适用场景：
- 个股分钟线数据
- T+0交易机制
- 高频短线交易
- 日内套利策略

注意事项：
- 本策略仅用于回测，不适用于A股T+1市场
- 需要考虑交易成本对收益的影响
- 适合波动较大的股票
- 建议在模拟环境中充分测试
"""

# 导入必要的数据处理和技术分析库
import numpy as np   # 数值计算库，用于数组运算
import pandas as pd  # 数据处理库，用于数据框操作
import talib         # 技术分析库，用于计算MACD等技术指标

def init(ContextInfo):
    """
    策略初始化函数

    功能：设置日内回转策略的基本参数和初始状态

    参数：
        ContextInfo: 策略运行环境对象

    初始化内容：
    1. 设置持仓跟踪字典
    2. 配置股票池（单只股票）
    3. 设置交易参数和状态变量
    4. 配置回测账户信息

    关键变量：
        MarketPosition: 持仓跟踪字典
        first: 首次交易标志
        Lots: 每次交易手数
        total: 基础持仓手数
        day: 日期跟踪列表
    """

    # === 第一步：初始化持仓跟踪系统 ===

    # 创建持仓跟踪字典，用于记录策略内部的持仓状态
    # 这是因为需要区分基础持仓和日内交易产生的持仓变化
    MarketPosition = {}
    ContextInfo.MarketPosition = MarketPosition

    # === 第二步：设置股票池 ===

    # 设置股票池为当前选择的单只股票
    # 日内回转策略通常专注于单只股票的深度交易
    ContextInfo.set_universe([ContextInfo.stockcode + '.' + ContextInfo.market])

    # === 第三步：初始化交易参数 ===

    # 首次交易标志，用于控制基础建仓
    ContextInfo.first = 0

    # 设定每次日内交易的手数（1手=100股）
    ContextInfo.Lots = 100

    # 日期跟踪列表，用于记录交易日期变化
    ContextInfo.day = [0, 0]

    # 收盘标志（暂未使用）
    ContextInfo.ending = 0

    # 基础持仓手数，策略的核心持仓水平
    # 日内交易围绕这个基础持仓进行，收盘前必须回转到此水平
    ContextInfo.total = 100

    # === 第四步：设置回测账户 ===

    # 设置回测账户ID
    ContextInfo.accountID = 'testS'


def handlebar(ContextInfo):
    """
    日内回转策略核心处理函数

    功能：执行基于MACD指标的日内回转交易策略

    参数：
        ContextInfo: 策略运行环境对象

    执行流程：
    1. 数据充足性检查（至少35个数据点）
    2. 获取历史价格数据用于MACD计算
    3. 首次建仓（建立基础持仓）
    4. 日内交易信号判断和执行
    5. 收盘前仓位回转操作

    交易时机：
    - 开仓：MACD从负转正
    - 平仓：MACD从正转负
    - 回转：14:55后调整至基础仓位

    风控要点：
    - 确保有足够资金和持仓
    - 严格控制交易时间
    - 强制收盘前回转
    """

    # === 第一步：基础数据检查 ===

    # 获取当前K线位置
    d = ContextInfo.barpos

    # 确保有足够的历史数据计算MACD（至少需要35个数据点）
    if d < 35:
        return

    # === 第二步：时间和数据获取 ===

    # 计算数据获取的时间范围（过去35个时间点）
    startdate = timetag_to_datetime(ContextInfo.get_bar_timetag(d - 35), '%Y%m%d%H%M%S')
    enddate = timetag_to_datetime(ContextInfo.get_bar_timetag(d), '%Y%m%d%H%M%S')

    # 获取当前时间并格式化为可读格式
    date = timetag_to_datetime(ContextInfo.get_bar_timetag(d), '%Y-%m-%d %H:%M:%S')
    print('日期', date)

    # 初始化交易状态标志
    flage = False        # 交易方向标志（True=买入，False=卖出）
    singleemited = False # 是否执行了交易操作

    # === 第三步：获取市场数据 ===

    # 获取指定时间范围内的收盘价数据
    # 用于计算MACD技术指标
    df = ContextInfo.get_market_data(['close'], stock_code=ContextInfo.get_universe(),
                                   start_time=startdate, end_time=enddate,
                                   period=ContextInfo.period)

    # 检查数据有效性
    if df.empty:
        return

    # === 第四步：首次建仓逻辑 ===

    # 如果是首次运行，建立基础持仓
    if ContextInfo.first == 0:

        # 执行基础建仓：买入指定数量的股票
        # 参数说明：
        # - 股票代码：ContextInfo.get_universe()[0]
        # - 数量：ContextInfo.total手（基础持仓）
        # - 价格类型：'fix'（固定价格）
        # - 价格：当前收盘价 df.iloc[-1, 0]
        order_shares(ContextInfo.get_universe()[0], ContextInfo.total, 'fix',
                    df.iloc[-1, 0], ContextInfo, ContextInfo.accountID)

        # 设置交易状态
        flage = True
        singleemited = True

        # 标记已完成首次建仓
        ContextInfo.first = 1

        # 记录当前日期
        ContextInfo.day[-1] = date[8:10]

        # 初始化回转统计
        ContextInfo.turnaround = [0, 0]

        # 更新内部持仓记录
        ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] = ContextInfo.total

        # 首次建仓后直接返回，等待下一个周期开始日内交易
        return

    # === 第五步：日内交易主逻辑 ===

    # 更新当前日期记录
    ContextInfo.day[0] = date

    # 交易时间控制：14:55:00之后不再进行新的交易
    # 这是为了确保有足够时间进行收盘前的仓位回转
    if int(date[-8:-6] + date[-5:-3]) > 1455:
        return

    # 获取当前账户可用资金
    avaliable = get_avaliable(ContextInfo.accountID, 'STOCK')

    # 获取当前实际持仓情况
    holding = get_holdings(ContextInfo.accountID, 'STOCK')

    # 确保目标股票在持仓字典中（如果没有持仓则设为0）
    if ContextInfo.get_universe()[0] not in list(holding.keys()):
        holding[ContextInfo.get_universe()[0]] = 0

    # === 第六步：MACD指标计算和交易信号判断 ===

    # 检查基础持仓是否有效
    if ContextInfo.total >= 0:

        # 提取最近35个收盘价数据用于MACD计算
        recent_date = np.array(df.iloc[-35:, 0])

        # 计算当前和前一个周期的MACD值
        # talib.MACD返回(macd_line, signal_line, histogram)，我们使用macd_line
        macd = talib.MACD(recent_date)[0][-1]      # 当前MACD值
        macd_pre = talib.MACD(recent_date)[0][-2]  # 前一周期MACD值

        # === 第七步：交易信号执行 ===

        # 判断是否为收盘前的回转时间（14:55）
        if date[-8:-3] != '14:55':

            # --- 买入信号：MACD从负转正 ---
            if macd > 0 and macd_pre < 0:

                # 资金充足性检查：确保有足够资金买入
                if avaliable > df.iloc[-1, 0] * ContextInfo.Lots * 100:

                    # 执行买入操作
                    order_shares(ContextInfo.get_universe()[0], ContextInfo.Lots, 'fix',
                               df.iloc[-1, 0], ContextInfo, ContextInfo.accountID)

                    # 更新交易状态
                    flage = True
                    singleemited = True

                    # 更新内部持仓记录
                    ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] += ContextInfo.Lots

                    # 输出交易信息
                    print(ContextInfo.get_universe()[0], 'open position at market price', ContextInfo.Lots, '股')

            # --- 卖出信号：MACD从正转负 ---
            elif (macd < 0 and macd_pre > 0 and
                  holding[ContextInfo.get_universe()[0]] >= ContextInfo.Lots):

                # 执行卖出操作（负数表示卖出）
                order_shares(ContextInfo.get_universe()[0], -ContextInfo.Lots, 'fix',
                           df.iloc[-1, 0], ContextInfo, ContextInfo.accountID)

                # 更新交易状态
                flage = False
                singleemited = True

                # 输出交易信息
                print(ContextInfo.get_universe()[0], 'close position at market price', ContextInfo.Lots, '股')

                # 更新内部持仓记录
                ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] -= ContextInfo.Lots

        # === 第八步：收盘前仓位回转操作 ===

        else:  # 14:55时执行仓位回转

            # --- 处理超额持仓：如果当前持仓大于基础持仓，卖出多余部分 ---
            if ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] > ContextInfo.total:

                # 计算需要卖出的数量
                sell_amount = ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] - ContextInfo.total

                # 执行卖出操作
                order_shares(ContextInfo.get_universe()[0], -sell_amount, 'fix',
                           df.iloc[-1, 0], ContextInfo, ContextInfo.accountID)

                # 更新状态
                flage = False
                singleemited = True

                # 调整内部持仓记录至基础水平
                ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] = ContextInfo.total

            # --- 处理不足持仓：如果当前持仓小于基础持仓，买入不足部分 ---
            if ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] < ContextInfo.total:

                # 计算需要买入的数量
                buy_amount = ContextInfo.total - ContextInfo.MarketPosition[ContextInfo.get_universe()[0]]

                # 执行买入操作
                order_shares(ContextInfo.get_universe()[0], buy_amount, 'fix',
                           df.iloc[-1, 0], ContextInfo, ContextInfo.accountID)

                # 更新状态
                flage = True
                singleemited = True

                # 调整内部持仓记录至基础水平
                ContextInfo.MarketPosition[ContextInfo.get_universe()[0]] = ContextInfo.total

        # === 第九步：图表绘制（可选功能，已注释） ===

        # 以下代码用于在图表上显示买卖信号，可根据需要启用
        '''
        ContextInfo.day[-1] = ContextInfo.day[0]
        if singleemited:
            if flage:
                ContextInfo.paint('do_buy',1,-1,0,"yellow",'noaxis')
                ContextInfo.paint('do_sell',0,-1,0,"red",'noaxis')
            else:
                ContextInfo.paint('do_buy',0,-1,0,"yellow",'noaxis')
                ContextInfo.paint('do_sell',1,-1,0,"red",'noaxis')
        '''


# 可选的持仓监控图表绘制
# ContextInfo.paint('holding',ContextInfo.MarketPosition[ContextInfo.get_universe()[0]],-1,0)

def get_avaliable(accountid, datatype):
    """
    获取账户可用资金

    功能：查询指定账户的可用资金余额

    参数：
        accountid: 账户ID字符串
        datatype: 账户类型（如"STOCK"表示股票账户）

    返回值：
        result: 可用资金金额（浮点数，单位：元）

    用途：
        - 日内交易前的资金充足性检查
        - 确保有足够资金执行买入操作
        - 风险控制和资金管理

    注意：
        - 返回的是可用资金，不包括冻结资金
        - 实时数据，反映当前账户状态
        - 用于高频交易的资金监控
    """
    # 初始化可用资金为0
    result = 0

    # 调用QMT API获取账户资金信息
    resultlist = get_trade_detail_data(accountid, datatype, "ACCOUNT")

    # 遍历账户信息（通常只有一条记录）
    for obj in resultlist:
        # 获取可用资金金额
        result = obj.m_dAvailable

    return result

def get_holdings(accountid, datatype):
    """
    获取账户持仓信息（可用数量版本）

    功能：查询指定账户的股票持仓情况，返回可用数量

    参数：
        accountid: 账户ID字符串
        datatype: 账户类型（如"STOCK"表示股票账户）

    返回值：
        holdinglist: 持仓字典，格式为{股票代码: 可用股数}

    数据特点：
        - 使用m_nCanUseVolume（可用数量）而非总持仓
        - 可用数量 = 总持仓 - 冻结数量
        - 适用于T+0交易的实时持仓查询

    用途：
        - 日内交易前的持仓检查
        - 确保有足够持仓执行卖出操作
        - 仓位回转时的持仓核对

    注意：
        - 返回的是股数，不是手数
        - 仅包含可用于交易的部分
        - 实时数据，适合高频交易使用
    """
    # 初始化持仓字典
    holdinglist = {}

    # 调用QMT API获取持仓明细数据
    resultlist = get_trade_detail_data(accountid, datatype, "POSITION")

    # 遍历持仓记录，构建持仓字典
    for obj in resultlist:
        # 构建完整的股票代码（证券代码.交易所代码）
        stock_code = obj.m_strInstrumentID + "." + obj.m_strExchangeID

        # 记录可用股数（注意：这里是股数，不是手数）
        holdinglist[stock_code] = obj.m_nCanUseVolume

    return holdinglist










