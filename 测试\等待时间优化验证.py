#coding:gbk

"""
等待时间优化验证
测试参数调整后的策略启动时间改进
"""

def calculate_waiting_time(cmf_n, bias_n, adx_n, vae_period):
    """
    计算策略等待时间
    
    参数:
        cmf_n: CMF计算周期
        bias_n: BIAS计算周期  
        adx_n: ADX计算周期
        vae_period: VAE计算周期
    
    返回:
        int: 需要等待的K线数量
    """
    return max(cmf_n, bias_n, adx_n, vae_period * 2) + 10

def analyze_optimization():
    """分析参数优化效果"""
    print("="*80)
    print("📊 策略等待时间优化分析")
    print("="*80)
    
    # 原始参数
    original = {
        'name': '原始参数',
        'cmf_n': 30,
        'bias_n': 30,
        'adx_n': 23,
        'vae_period': 20
    }
    
    # 优化方案
    scenarios = [
        original,
        {
            'name': '仅优化VAE',
            'cmf_n': 30,
            'bias_n': 30,
            'adx_n': 23,
            'vae_period': 15
        },
        {
            'name': '适度优化(推荐)',
            'cmf_n': 25,
            'bias_n': 25,
            'adx_n': 20,
            'vae_period': 17
        },
        {
            'name': '激进优化',
            'cmf_n': 15,
            'bias_n': 15,
            'adx_n': 15,
            'vae_period': 10
        },
        {
            'name': '保守优化',
            'cmf_n': 25,
            'bias_n': 25,
            'adx_n': 20,
            'vae_period': 15
        }
    ]
    
    print(f"{'方案':<12} {'CMF':<4} {'BIAS':<4} {'ADX':<4} {'VAE':<4} {'等待K线':<8} {'减少量':<8} {'减少比例'}")
    print("-" * 80)
    
    original_waiting = calculate_waiting_time(
        original['cmf_n'], original['bias_n'], 
        original['adx_n'], original['vae_period']
    )
    
    for scenario in scenarios:
        waiting_time = calculate_waiting_time(
            scenario['cmf_n'], scenario['bias_n'],
            scenario['adx_n'], scenario['vae_period']
        )
        
        reduction = original_waiting - waiting_time
        reduction_pct = (reduction / original_waiting * 100) if original_waiting > 0 else 0
        
        print(f"{scenario['name']:<12} "
              f"{scenario['cmf_n']:<4} "
              f"{scenario['bias_n']:<4} "
              f"{scenario['adx_n']:<4} "
              f"{scenario['vae_period']:<4} "
              f"{waiting_time:<8} "
              f"{reduction:+d}根     "
              f"{reduction_pct:+.1f}%")

def analyze_tradeoffs():
    """分析优化权衡"""
    print("\n" + "="*80)
    print("⚖️ 参数优化权衡分析")
    print("="*80)
    
    print("📈 各指标周期减少的影响:")
    print("-" * 50)
    
    indicators = [
        {
            'name': 'CMF (资金流)',
            'original': 30,
            'optimized': 25,
            'impact': '背离识别精度基本保持，25周期足够识别资金流向变化'
        },
        {
            'name': 'BIAS (乖离率)',
            'original': 30,
            'optimized': 25,
            'impact': '价格偏离均线的判断基础适度减少，25周期仍很稳定'
        },
        {
            'name': 'ADX (趋势强度)',
            'original': 23,
            'optimized': 20,
            'impact': '趋势强度判断略微敏感，但20周期仍保持良好稳定性'
        },
        {
            'name': 'VAE (波动率)',
            'original': 20,
            'optimized': 17,
            'impact': '波动率计算基础轻微减少，对动态止盈止损影响很小'
        }
    ]
    
    for indicator in indicators:
        reduction = indicator['original'] - indicator['optimized']
        reduction_pct = reduction / indicator['original'] * 100
        
        print(f"{indicator['name']:<15}: {indicator['original']} → {indicator['optimized']} "
              f"(-{reduction}, -{reduction_pct:.1f}%)")
        print(f"                影响: {indicator['impact']}")
        print()

def time_savings_analysis():
    """时间节省分析"""
    print("⏰ 实际时间节省分析")
    print("-" * 50)
    
    # 不同时间周期的K线对应时间
    timeframes = [
        {'name': '1分钟', 'minutes_per_bar': 1},
        {'name': '5分钟', 'minutes_per_bar': 5},
        {'name': '15分钟', 'minutes_per_bar': 15},
        {'name': '30分钟', 'minutes_per_bar': 30},
        {'name': '1小时', 'minutes_per_bar': 60},
        {'name': '日线', 'minutes_per_bar': 1440}  # 24小时
    ]
    
    bars_saved = 6   # 从50根减少到44根，节省6根K线
    
    print(f"{'时间周期':<8} {'节省K线数':<10} {'节省时间'}")
    print("-" * 40)
    
    for tf in timeframes:
        total_minutes = bars_saved * tf['minutes_per_bar']
        
        if total_minutes < 60:
            time_str = f"{total_minutes}分钟"
        elif total_minutes < 1440:
            hours = total_minutes // 60
            minutes = total_minutes % 60
            time_str = f"{hours}小时{minutes}分钟" if minutes > 0 else f"{hours}小时"
        else:
            days = total_minutes // 1440
            hours = (total_minutes % 1440) // 60
            time_str = f"{days}天{hours}小时" if hours > 0 else f"{days}天"
        
        print(f"{tf['name']:<8} {bars_saved}根        {time_str}")

def recommendations():
    """给出建议"""
    print("\n" + "="*80)
    print("💡 优化建议")
    print("="*80)
    
    print("🎯 推荐方案：适度优化")
    print("- CMF_N: 30 → 25 (-17%)")
    print("- BIAS_N: 30 → 25 (-17%)")
    print("- ADX_N: 23 → 20 (-13%)")
    print("- VAE_周期: 20 → 17 (-15%)")
    print("- 等待时间: 50根 → 44根 (-12%)")
    
    print("\n✅ 优化优势:")
    print("1. 策略启动更快：减少6根K线等待时间")
    print("2. 响应略敏感：能适度更快捕捉市场变化")
    print("3. 平衡性佳：在速度和稳定性间取得最佳平衡")
    print("4. 风险很低：参数调整幅度保守，稳定性影响最小")
    
    print("\n⚠️ 注意事项:")
    print("1. 建议先在模拟环境测试优化效果")
    print("2. 关注是否增加假信号频率")
    print("3. 可根据实际效果进一步微调")
    print("4. 不同市场环境可能需要不同参数")
    
    print("\n🔄 如需进一步优化:")
    print("- 可以尝试更激进优化方案（减少到35-40根K线）")
    print("- 或者根据具体交易标的特征个性化调整")
    print("- 建议通过回测验证不同参数组合的效果")

if __name__ == "__main__":
    analyze_optimization()
    analyze_tradeoffs()
    time_savings_analysis()
    recommendations()
    
    print("\n" + "="*80)
    print("✅ 等待时间优化分析完成")
    print("💡 建议：采用15%适度优化方案，最佳平衡速度与稳定性")
    print("="*80)
