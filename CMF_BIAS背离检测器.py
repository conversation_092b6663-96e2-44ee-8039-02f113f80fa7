#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMF+BIAS双重背离检测器

基于策略研究\背离策略总结文档的完整实现
包含SKDJ超卖、CMF资金流背离、BIAS乖离率背离、ADX趋势强度、VAE动态风控
"""

import numpy as np
import talib
from typing import Dict, List, Optional, Tuple, Any

class CMFBIASDivergenceDetector:
    """CMF+BIAS双重背离检测器"""
    
    def __init__(self, 
                 SKDJ_N: int = 8, SKDJ_M: int = 4,
                 CMF_N: int = 30, CMF_M: int = 20,
                 BIAS_N: int = 30, BIAS_M: int = 20,
                 ADX_N: int = 23, ADX_M: int = 11,
                 VAE_基础TR: float = 1.8, VAE_初始止损: float = 1.5, VAE_周期: int = 20,
                 固定止损: float = 0.5):
        """
        初始化背离检测器
        
        Args:
            SKDJ_N: SKDJ计算周期
            SKDJ_M: SKDJ平滑周期
            CMF_N: CMF计算周期
            CMF_M: CMF背离判断周期
            BIAS_N: BIAS计算周期
            BIAS_M: BIAS背离判断周期
            ADX_N: ADX计算周期
            ADX_M: ADX平滑周期
            VAE_基础TR: VAE基础止盈倍数
            VAE_初始止损: VAE初始止损倍数
            VAE_周期: VAE计算周期
            固定止损: 固定止损百分比
        """
        self.SKDJ_N = SKDJ_N
        self.SKDJ_M = SKDJ_M
        self.CMF_N = CMF_N
        self.CMF_M = CMF_M
        self.BIAS_N = BIAS_N
        self.BIAS_M = BIAS_M
        self.ADX_N = ADX_N
        self.ADX_M = ADX_M
        self.VAE_基础TR = VAE_基础TR
        self.VAE_初始止损 = VAE_初始止损
        self.VAE_周期 = VAE_周期
        self.固定止损 = 固定止损
        
        # 计算所需的最小数据量
        self.min_data_length = max(CMF_N, BIAS_N, ADX_N, VAE_周期 * 2) + 10
    
    def calculate_SKDJ(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算SKDJ超卖指标"""
        try:
            # LOWV = LLV(LOW, SKDJ_N)
            LOWV = talib.MIN(lows, timeperiod=self.SKDJ_N)
            # HIGHV = HHV(HIGH, SKDJ_N)
            HIGHV = talib.MAX(highs, timeperiod=self.SKDJ_N)
            
            # RSV = EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100, SKDJ_M)
            RSV_raw = (closes - LOWV) / (HIGHV - LOWV) * 100
            RSV_raw = np.nan_to_num(RSV_raw, nan=50.0)  # 处理除零情况
            RSV = talib.EMA(RSV_raw, timeperiod=self.SKDJ_M)
            
            # K = EMA(RSV, SKDJ_M)
            K = talib.EMA(RSV, timeperiod=self.SKDJ_M)
            # D = MA(K, SKDJ_M)
            D = talib.SMA(K, timeperiod=self.SKDJ_M)
            
            return K, D
        except Exception as e:
            print(f"⚠️ SKDJ计算失败: {e}")
            return np.full(len(closes), 50.0), np.full(len(closes), 50.0)
    
    def calculate_CMF(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray, volumes: np.ndarray) -> np.ndarray:
        """计算CMF资金流指标"""
        try:
            # CLV = (CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW)
            CLV = (closes - lows - highs + closes) / (highs - lows)
            CLV = np.nan_to_num(CLV, nan=0.0)  # 处理除零情况
            
            # MF = CLV * VOL
            MF = CLV * volumes
            
            # CMF = SUM(MF, CMF_N) / SUM(VOL, CMF_N)
            MF_sum = talib.SUM(MF, timeperiod=self.CMF_N)
            VOL_sum = talib.SUM(volumes, timeperiod=self.CMF_N)
            CMF = MF_sum / VOL_sum
            CMF = np.nan_to_num(CMF, nan=0.0)
            
            return CMF
        except Exception as e:
            print(f"⚠️ CMF计算失败: {e}")
            return np.zeros(len(closes))
    
    def calculate_BIAS(self, closes: np.ndarray) -> np.ndarray:
        """计算BIAS乖离率指标"""
        try:
            # BIAS = (CLOSE-MA(CLOSE,BIAS_N))/MA(CLOSE,BIAS_N)*100
            MA_close = talib.SMA(closes, timeperiod=self.BIAS_N)
            BIAS = (closes - MA_close) / MA_close * 100
            BIAS = np.nan_to_num(BIAS, nan=0.0)
            
            return BIAS
        except Exception as e:
            print(f"⚠️ BIAS计算失败: {e}")
            return np.zeros(len(closes))
    
    def calculate_ADX(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> np.ndarray:
        """计算ADX趋势强度指标"""
        try:
            # 使用talib的ADX函数，它内部实现了完整的ADX计算逻辑
            ADX = talib.ADX(highs, lows, closes, timeperiod=self.ADX_N)
            
            # 对ADX进行平滑处理
            ADX_smoothed = talib.SMA(ADX, timeperiod=self.ADX_M)
            ADX_smoothed = np.nan_to_num(ADX_smoothed, nan=20.0)
            
            return ADX_smoothed
        except Exception as e:
            print(f"⚠️ ADX计算失败: {e}")
            return np.full(len(closes), 20.0)
    
    def detect_CMF_divergence(self, highs: np.ndarray, lows: np.ndarray, CMF: np.ndarray) -> Tuple[bool, bool]:
        """检测CMF背离信号"""
        try:
            if len(CMF) < self.CMF_M:
                return False, False
            
            # CMF_HH = HIGH >= HHV(HIGH, CMF_M)
            HHV_high = talib.MAX(highs, timeperiod=self.CMF_M)
            CMF_HH = highs[-1] >= HHV_high[-1]
            
            # CMF_LL = LOW <= LLV(LOW, CMF_M)
            LLV_low = talib.MIN(lows, timeperiod=self.CMF_M)
            CMF_LL = lows[-1] <= LLV_low[-1]
            
            # CMF_指标HH = CMF >= HHV(CMF, CMF_M)
            HHV_CMF = talib.MAX(CMF, timeperiod=self.CMF_M)
            CMF_指标HH = CMF[-1] >= HHV_CMF[-1]
            
            # CMF_指标LL = CMF <= LLV(CMF, CMF_M)
            LLV_CMF = talib.MIN(CMF, timeperiod=self.CMF_M)
            CMF_指标LL = CMF[-1] <= LLV_CMF[-1]
            
            # CMF顶背离 = CMF_HH AND CMF_指标HH==0 AND CMF>0
            CMF顶背离 = CMF_HH and not CMF_指标HH and CMF[-1] > 0
            
            # CMF底背离 = CMF_LL AND CMF_指标LL==0 AND CMF<0
            CMF底背离 = CMF_LL and not CMF_指标LL and CMF[-1] < 0
            
            return CMF底背离, CMF顶背离
        except Exception as e:
            print(f"⚠️ CMF背离检测失败: {e}")
            return False, False
    
    def detect_BIAS_divergence(self, highs: np.ndarray, lows: np.ndarray, BIAS: np.ndarray) -> Tuple[bool, bool]:
        """检测BIAS背离信号"""
        try:
            if len(BIAS) < self.BIAS_M:
                return False, False
            
            # BIAS_HH = HIGH >= HHV(HIGH, BIAS_M)
            HHV_high = talib.MAX(highs, timeperiod=self.BIAS_M)
            BIAS_HH = highs[-1] >= HHV_high[-1]
            
            # BIAS_LL = LOW <= LLV(LOW, BIAS_M)
            LLV_low = talib.MIN(lows, timeperiod=self.BIAS_M)
            BIAS_LL = lows[-1] <= LLV_low[-1]
            
            # BIAS_指标HH = BIAS >= HHV(BIAS, BIAS_M)
            HHV_BIAS = talib.MAX(BIAS, timeperiod=self.BIAS_M)
            BIAS_指标HH = BIAS[-1] >= HHV_BIAS[-1]
            
            # BIAS_指标LL = BIAS <= LLV(BIAS, BIAS_M)
            LLV_BIAS = talib.MIN(BIAS, timeperiod=self.BIAS_M)
            BIAS_指标LL = BIAS[-1] <= LLV_BIAS[-1]
            
            # BIAS顶背离 = BIAS_HH AND BIAS_指标HH==0 AND BIAS>0
            BIAS顶背离 = BIAS_HH and not BIAS_指标HH and BIAS[-1] > 0
            
            # BIAS底背离 = BIAS_LL AND BIAS_指标LL==0 AND BIAS<0
            BIAS底背离 = BIAS_LL and not BIAS_指标LL and BIAS[-1] < 0
            
            return BIAS底背离, BIAS顶背离
        except Exception as e:
            print(f"⚠️ BIAS背离检测失败: {e}")
            return False, False
    
    def calculate_VAE_dynamic_control(self, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray) -> Dict[str, float]:
        """计算VAE动态风控"""
        try:
            # 当前ATR = ATR(VAE_周期)
            当前ATR = talib.ATR(highs, lows, closes, timeperiod=self.VAE_周期)
            # ATR均值 = MA(当前ATR, VAE_周期*2)
            ATR均值 = talib.SMA(当前ATR, timeperiod=self.VAE_周期 * 2)
            
            if len(当前ATR) == 0 or len(ATR均值) == 0:
                return {'动态TR': self.VAE_基础TR, '波动率比值': 1.0, '波动率区间': '正常波动区'}
            
            # 波动率比值 = 当前ATR / ATR均值
            波动率比值 = 当前ATR[-1] / ATR均值[-1] if ATR均值[-1] > 0 else 1.0
            
            # 波动率分区判断
            if 波动率比值 <= 0.8:
                波动率区间 = '低波动区'
                动态TR = self.VAE_基础TR * 2
            elif 波动率比值 > 0.8 and 波动率比值 <= 1.2:
                波动率区间 = '正常波动区'
                动态TR = self.VAE_基础TR
            elif 波动率比值 > 1.2 and 波动率比值 <= 1.8:
                波动率区间 = '高波动区'
                动态TR = self.VAE_基础TR * 1
            else:  # 波动率比值 > 1.8
                波动率区间 = '极高波动区'
                动态TR = self.VAE_基础TR * 0.7
            
            return {
                '动态TR': 动态TR,
                '波动率比值': 波动率比值,
                '波动率区间': 波动率区间,
                '当前ATR': 当前ATR[-1],
                'ATR均值': ATR均值[-1]
            }
        except Exception as e:
            print(f"⚠️ VAE动态风控计算失败: {e}")
            return {'动态TR': self.VAE_基础TR, '波动率比值': 1.0, '波动率区间': '正常波动区'}
    
    def get_comprehensive_signals(self, klines: List[Dict]) -> Dict[str, Any]:
        """获取综合交易信号"""
        try:
            if len(klines) < self.min_data_length:
                return {
                    'status': 'insufficient_data',
                    'message': f'需要至少{self.min_data_length}根K线，当前只有{len(klines)}根',
                    'buy_signal': False,
                    'sell_signal': False
                }
            
            # 提取OHLCV数据
            opens = np.array([k['open'] for k in klines], dtype=np.float64)
            highs = np.array([k['high'] for k in klines], dtype=np.float64)
            lows = np.array([k['low'] for k in klines], dtype=np.float64)
            closes = np.array([k['close'] for k in klines], dtype=np.float64)
            volumes = np.array([k['volume'] for k in klines], dtype=np.float64)
            
            # 计算各项指标
            K, D = self.calculate_SKDJ(highs, lows, closes)
            CMF = self.calculate_CMF(highs, lows, closes, volumes)
            BIAS = self.calculate_BIAS(closes)
            ADX = self.calculate_ADX(highs, lows, closes)
            VAE_info = self.calculate_VAE_dynamic_control(highs, lows, closes)
            
            # 检测背离信号
            CMF底背离, CMF顶背离 = self.detect_CMF_divergence(highs, lows, CMF)
            BIAS底背离, BIAS顶背离 = self.detect_BIAS_divergence(highs, lows, BIAS)
            
            # 计算阻力线突破
            K线加权均值 = (highs + lows + 2 * closes) / 4
            阻力线 = K线加权均值 + (K线加权均值 - lows)
            突破条件 = closes[-1] > 阻力线[-1] and closes[-2] <= 阻力线[-2] if len(closes) > 1 else False
            
            # 买入条件组合
            SKDJ超卖 = K[-1] < 20 and D[-1] < 20
            
            # 双重背离（允许前1-2根K线的背离信号）
            双重背离 = (CMF底背离 or (len(klines) > 1 and self.check_previous_CMF_divergence(klines[:-1])) or 
                      (len(klines) > 2 and self.check_previous_CMF_divergence(klines[:-2]))) and \
                     (BIAS底背离 or (len(klines) > 1 and self.check_previous_BIAS_divergence(klines[:-1])) or 
                      (len(klines) > 2 and self.check_previous_BIAS_divergence(klines[:-2])))
            
            强趋势确认 = ADX[-1] > 40
            突破确认 = 突破条件
            
            # 最终买入信号
            买入信号 = SKDJ超卖 and 双重背离 and 强趋势确认 and 突破确认
            
            # 卖出条件（这里主要返回信息，具体卖出逻辑在策略中实现）
            卖出信号 = False  # 卖出信号需要结合持仓成本计算
            
            return {
                'status': 'success',
                'buy_signal': 买入信号,
                'sell_signal': 卖出信号,
                'indicators': {
                    'SKDJ_K': K[-1],
                    'SKDJ_D': D[-1],
                    'CMF': CMF[-1],
                    'BIAS': BIAS[-1],
                    'ADX': ADX[-1],
                    'resistance_line': 阻力线[-1],
                    'current_price': closes[-1]
                },
                'conditions': {
                    'SKDJ超卖': SKDJ超卖,
                    'CMF底背离': CMF底背离,
                    'BIAS底背离': BIAS底背离,
                    '双重背离': 双重背离,
                    '强趋势确认': 强趋势确认,
                    '突破确认': 突破确认
                },
                'VAE_info': VAE_info,
                'data_quality': {
                    'total_bars': len(klines),
                    'min_required': self.min_data_length,
                    'data_coverage': len(klines) / self.min_data_length
                }
            }
            
        except Exception as e:
            return {
                'status': 'calculation_error',
                'error_message': str(e),
                'buy_signal': False,
                'sell_signal': False
            }
    
    def check_previous_CMF_divergence(self, klines: List[Dict]) -> bool:
        """检查前面K线的CMF背离信号"""
        try:
            if len(klines) < self.CMF_M:
                return False
            
            highs = np.array([k['high'] for k in klines], dtype=np.float64)
            lows = np.array([k['low'] for k in klines], dtype=np.float64)
            closes = np.array([k['close'] for k in klines], dtype=np.float64)
            volumes = np.array([k['volume'] for k in klines], dtype=np.float64)
            
            CMF = self.calculate_CMF(highs, lows, closes, volumes)
            CMF底背离, _ = self.detect_CMF_divergence(highs, lows, CMF)
            
            return CMF底背离
        except:
            return False
    
    def check_previous_BIAS_divergence(self, klines: List[Dict]) -> bool:
        """检查前面K线的BIAS背离信号"""
        try:
            if len(klines) < self.BIAS_M:
                return False
            
            highs = np.array([k['high'] for k in klines], dtype=np.float64)
            lows = np.array([k['low'] for k in klines], dtype=np.float64)
            closes = np.array([k['close'] for k in klines], dtype=np.float64)
            
            BIAS = self.calculate_BIAS(closes)
            BIAS底背离, _ = self.detect_BIAS_divergence(highs, lows, BIAS)
            
            return BIAS底背离
        except:
            return False
