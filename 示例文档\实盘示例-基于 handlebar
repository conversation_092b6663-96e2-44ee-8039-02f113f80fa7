#coding:gbk

"""
双均线实盘交易策略（基于Handlebar框架）
==========================================

策略概述：
本策略是双均线交叉系统的实盘交易版本，适用于真实的股票交易环境。
通过计算快速和慢速移动平均线的交叉信号来执行实际的买卖操作。

策略特点：
- 适用环境：实盘交易（非回测）
- 交易标的：单只股票
- 信号系统：双均线交叉
- 资金管理：固定金额买入
- 风控措施：防重复下单、交易时间控制

技术指标：
- 快线：17日简单移动平均线
- 慢线：27日简单移动平均线

交易逻辑：
- 金叉买入：快线上穿慢线时买入固定金额
- 死叉卖出：快线下穿慢线时全部平仓

风控要点：
1. 仅在交易时间内执行（09:30-15:00）
2. 跳过历史K线，仅处理实时数据
3. 检查账户登录状态和资金充足性
4. 防止重复下单（委托确认机制）
5. 区分不同账户类型的操作代码

注意事项：
- 本策略用于实盘交易，请谨慎使用
- 建议先在模拟环境中充分测试
- 需要配置真实的交易账户信息
- 交易有风险，投资需谨慎
"""

# 导入必要的数据处理和时间处理库
import pandas as pd  # 数据处理库，用于处理市场数据
import numpy as np   # 数值计算库，用于计算移动平均线
import datetime      # 日期时间库，用于交易时间判断

class a():
    """
    全局状态保存类

    说明：
    - 用于保存策略运行过程中的状态信息
    - 避免使用ContextInfo保存状态（会被系统重置）
    - 确保委托状态和策略参数的持久性
    """
    pass

# 创建全局状态实例，用于保存策略状态和委托信息
A = a()

def init(C):
    """
    策略初始化函数（实盘版本）

    功能：设置实盘交易的基本参数和账户配置

    参数：
        C: ContextInfo对象，包含策略运行环境信息

    全局变量设置：
        A.stock: 交易标的证券代码
        A.acct: 实际交易账户ID
        A.acct_type: 账户类型（STOCK/CREDIT）
        A.amount: 单笔买入金额
        A.line1/A.line2: 快慢均线周期
        A.waiting_list: 待确认委托列表
        A.buy_code/A.sell_code: 买卖操作代码

    重要提醒：
        - 账户信息来自模型交易界面的实际配置
        - 请确保账户已正确登录且有足够权限
        - 建议先在模拟账户中测试策略
    """
    # 构建完整的证券代码（代码.市场）
    A.stock = C.stockcode + '.' + C.market

    # 获取实际交易账户信息（从模型交易界面配置）
    A.acct = account          # 真实交易账户ID
    A.acct_type = accountType # 账户类型：STOCK(普通) 或 CREDIT(信用)

    # 交易参数设置
    A.amount = 10000  # 单笔买入金额，单位：元
    A.line1 = 17      # 快速移动平均线周期
    A.line2 = 27      # 慢速移动平均线周期

    # 委托管理：防止重复下单的关键机制
    A.waiting_list = []  # 未确认委托列表

    # 根据账户类型设置正确的买卖操作代码
    # 普通账户：23买入，24卖出
    # 信用账户：33买入，34卖出
    A.buy_code = 23 if A.acct_type == 'STOCK' else 33
    A.sell_code = 24 if A.acct_type == 'STOCK' else 34

    # 输出策略初始化信息，便于监控
    print(f'双均线实盘示例{A.stock} {A.acct} {A.acct_type} 单笔买入金额{A.amount}')

def handlebar(C):
    """
    实盘交易核心处理函数

    功能：在每根K线完成时执行双均线实盘交易策略

    参数：
        C: ContextInfo对象，包含当前K线和市场数据

    执行流程：
    1. 实时数据验证（跳过历史K线）
    2. 交易时间检查（仅在开盘时间执行）
    3. 账户状态验证（登录状态、资金充足性）
    4. 委托状态管理（防重复下单）
    5. 持仓信息获取
    6. 技术指标计算（双均线）
    7. 交易信号判断和实盘下单

    实盘特色：
    - 使用真实账户进行交易
    - 严格的时间和状态检查
    - 完善的风控机制
    - 实时委托状态跟踪

    风险提示：
    - 本函数执行真实交易操作
    - 请确保策略逻辑正确
    - 建议设置合理的止损机制
    """

    # === 第一步：实时数据验证 ===

    # 跳过历史K线，仅处理最新的实时K线
    # 这是实盘交易的关键安全措施，避免在历史数据上执行交易
    if not C.is_last_bar():
        return

    # === 第二步：交易时间检查 ===

    # 获取当前系统时间
    now = datetime.datetime.now()
    now_time = now.strftime('%H%M%S')

    # 严格限制交易时间：仅在09:30:00-15:00:00之间执行
    # 避免在集合竞价、午休、盘后时间执行交易
    if now_time < '093000' or now_time > "150000":
        return

    # === 第三步：账户状态验证 ===

    # 获取真实交易账户信息
    account = get_trade_detail_data(A.acct, A.acct_type, 'account')

    # 检查账户登录状态
    if len(account) == 0:
        print(f'账号{A.acct} 未登录 请检查')
        return

    # 提取账户信息并获取可用资金
    account = account[0]
    available_cash = int(account.m_dAvailable)  # 可用资金

    # === 第四步：委托状态管理（防重复下单的关键机制） ===

    # 检查是否有待确认的委托
    if A.waiting_list:
        found_list = []  # 已确认的委托列表

        # 查询成交记录，确认委托是否已执行
        # 注意：这里查询的是成交记录(deal)，而非委托记录(order)
        deals = get_trade_detail_data(A.acct, A.acct_type, 'deal')

        # 检查待确认委托是否已在成交记录中找到
        for deal in deals:
            if deal.m_strRemark in A.waiting_list:
                found_list.append(deal.m_strRemark)

        # 从待确认列表中移除已成交的委托
        A.waiting_list = [i for i in A.waiting_list if i not in found_list]

    # 如果仍有未确认的委托，暂停新的交易操作
    # 这是防止超单的重要安全机制
    if A.waiting_list:
        print(f"当前有未查到委托 {A.waiting_list} 暂停后续报单")
        return

    # === 第五步：持仓信息获取 ===

    # 获取当前真实持仓信息
    holdings = get_trade_detail_data(A.acct, A.acct_type, 'position')

    # 转换为字典格式：{证券代码: 可用数量}
    # 注意：使用m_nCanUseVolume（可用数量）而非m_nVolume（总持仓）
    holdings = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nCanUseVolume for i in holdings}

    # === 第六步：获取市场数据和技术指标计算 ===

    # 获取历史收盘价数据
    # 使用get_market_data_ex获取实时市场数据
    data = C.get_market_data_ex(["close"], [A.stock], period='1d', count=max(A.line1, A.line2)+1)
    close_list = data[A.stock].values

    # 检查数据充足性
    if len(close_list) < max(A.line1, A.line2)+1:
        print('行情长度不足(新上市或最近有停牌) 跳过运行')
        return

    # 计算前一日的快慢均线（用于判断穿越方向）
    pre_line1 = np.mean(close_list[-A.line1-1: -1])    # 前一日快线
    pre_line2 = np.mean(close_list[-A.line2-1: -1])    # 前一日慢线

    # 计算当前的快慢均线
    current_line1 = np.mean(close_list[-A.line1:])     # 当前快线
    current_line2 = np.mean(close_list[-A.line2:])     # 当前慢线

    # === 第七步：交易信号判断和实盘下单 ===

    # 计算买入数量：根据固定金额和当前价格
    vol = int(A.amount / close_list[-1] / 100) * 100  # 向下取整到100股

    # 买入条件：金叉信号 + 资金充足 + 无持仓 + 数量有效
    if (A.amount < available_cash and           # 可用资金充足
        vol >= 100 and                         # 买入数量至少100股
        A.stock not in holdings and            # 当前无持仓
        pre_line1 < pre_line2 and              # 前一日快线在慢线下方
        current_line1 > current_line2):        # 当前快线在慢线上方（金叉）

        # 执行实盘买入操作
        msg = f"双均线实盘 {A.stock} 上穿均线 买入 {vol}股"

        # 实盘下单参数说明：
        # A.buy_code: 买入操作代码（23普通/33信用）
        # 1101: 委托类型（限价委托）
        # A.acct: 真实交易账户
        # A.stock: 证券代码
        # 14: 价格类型（对手价）
        # -1: 价格（-1表示使用价格类型）
        # vol: 委托数量
        # '双均线实盘': 策略标识
        # 2: quickTrade参数（立即下单，适用于实盘）
        # msg: 委托备注信息
        # C: ContextInfo对象
        passorder(A.buy_code, 1101, A.acct, A.stock, 14, -1, vol, '双均线实盘', 2, msg, C)
        print(msg)
        A.waiting_list.append(msg)  # 加入待确认列表

    # 卖出条件：死叉信号 + 有持仓
    if (A.stock in holdings and                # 当前有持仓
        holdings[A.stock] > 0 and              # 持仓数量大于0
        pre_line1 > pre_line2 and              # 前一日快线在慢线上方
        current_line1 < current_line2):        # 当前快线在慢线下方（死叉）

        # 执行实盘卖出操作（全部清仓）
        msg = f"双均线实盘 {A.stock} 下穿均线 卖出 {holdings[A.stock]}股"

        # 卖出全部可用持仓
        passorder(A.sell_code, 1101, A.acct, A.stock, 14, -1, holdings[A.stock], '双均线实盘', 2, msg, C)
        print(msg)
        A.waiting_list.append(msg)  # 加入待确认列表
