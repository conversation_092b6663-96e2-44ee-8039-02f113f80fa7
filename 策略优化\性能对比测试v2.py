"""
CMF+BIAS双重背离策略性能对比测试 v2.0
========================================================================

测试目标：
1. 对比原版、v1.0优化版、v2.0优化版的性能差异
2. 验证计算结果的一致性
3. 分析缓存效率
4. 测试不同数据量下的性能表现

优化版本对比：
- 原版：基础实现
- v1.0：向量化计算 + pandas数据处理
- v2.0：统一指标引擎 + 智能缓存 + 批量处理
"""

import time
import numpy as np
import pandas as pd
import tracemalloc
from typing import Dict, List
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

class ComprehensivePerformanceTest:
    """全面性能测试套件"""
    
    def __init__(self):
        self.test_results = {}
        self.detectors = {}
        
    def setup_detectors(self):
        """初始化所有版本的检测器"""
        print("🔧 初始化检测器...")
        
        # 尝试导入v1.0优化版
        try:
            from CMF_BIAS背离检测器_优化版v1 import OptimizedCMFBIASDivergenceDetector
            self.detectors['v1.0'] = OptimizedCMFBIASDivergenceDetector()
            print("✅ v1.0优化版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ v1.0优化版导入失败: {e}")
        
        # 尝试导入v2.0优化版
        try:
            from CMF_BIAS背离检测器_优化版v2 import AdvancedCMFBIASDivergenceDetector
            self.detectors['v2.0'] = AdvancedCMFBIASDivergenceDetector()
            print("✅ v2.0优化版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ v2.0优化版导入失败: {e}")
        
        # 尝试导入原版
        try:
            sys.path.append(os.path.join(parent_dir, '框架'))
            from CMF_BIAS背离检测器 import CMFBIASDivergenceDetector
            self.detectors['原版'] = CMFBIASDivergenceDetector()
            print("✅ 原版检测器初始化成功")
        except ImportError as e:
            print(f"⚠️ 原版导入失败: {e}")
        
        print(f"📊 成功初始化 {len(self.detectors)} 个检测器版本")
        return len(self.detectors) > 0
        
    def generate_test_data(self, num_bars: int) -> List[Dict]:
        """生成测试用的K线数据"""
        np.random.seed(42)  # 固定随机种子确保可重复性
        
        base_price = 100.0
        klines = []
        
        for i in range(num_bars):
            # 模拟价格波动
            change = np.random.normal(0, 0.02)  # 2%的标准波动
            base_price *= (1 + change)
            
            # 生成OHLC数据
            high = base_price * (1 + abs(np.random.normal(0, 0.01)))
            low = base_price * (1 - abs(np.random.normal(0, 0.01)))
            close = base_price
            open_price = klines[-1]['close'] if klines else base_price
            volume = np.random.randint(1000, 10000)
            
            klines.append({
                'open': open_price,
                'high': max(high, open_price, close),
                'low': min(low, open_price, close),
                'close': close,
                'volume': volume,
                'timestamp': f"2024-01-{i+1:02d} 09:30:00"
            })
            
        return klines
    
    def measure_performance_advanced(self, detector, version: str, test_data: List[Dict], iterations: int = 5) -> Dict:
        """高级性能测量"""
        
        # 开始内存监控
        tracemalloc.start()
        
        # 预热
        try:
            if version == 'v1.0':
                detector.get_comprehensive_signals_optimized(test_data)
            elif version == 'v2.0':
                detector.get_comprehensive_signals_v2(test_data)
            else:
                detector.get_comprehensive_signals(test_data)
        except:
            pass
        
        # 性能测试
        start_time = time.perf_counter()
        results = []
        
        for i in range(iterations):
            try:
                if version == 'v1.0':
                    result = detector.get_comprehensive_signals_optimized(test_data)
                elif version == 'v2.0':
                    result = detector.get_comprehensive_signals_v2(test_data)
                else:
                    result = detector.get_comprehensive_signals(test_data)
                results.append(result)
            except Exception as e:
                print(f"❌ {version} 第{i+1}次执行失败: {e}")
                results.append({'status': 'error', 'error': str(e)})
        
        end_time = time.perf_counter()
        
        # 内存使用情况
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # 计算性能指标
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        # 成功率
        successful_results = [r for r in results if r.get('status') == 'success']
        success_rate = len(successful_results) / len(results)
        
        # 获取性能信息（如果支持）
        performance_info = {}
        if successful_results and 'performance_info' in successful_results[-1]:
            performance_info = successful_results[-1]['performance_info']
        
        return {
            'version': version,
            'total_time': total_time,
            'avg_time': avg_time,
            'iterations': iterations,
            'memory_current': current / 1024 / 1024,  # MB
            'memory_peak': peak / 1024 / 1024,  # MB
            'success_rate': success_rate,
            'results': successful_results[-1] if successful_results else None,
            'performance_info': performance_info
        }
    
    def compare_calculation_accuracy(self, results: Dict[str, Dict]) -> Dict:
        """比较计算精度"""
        comparison = {
            'accuracy_check': True,
            'differences': [],
            'signal_consistency': True
        }
        
        try:
            # 获取基准结果（优先使用原版，否则使用第一个可用版本）
            baseline_version = '原版' if '原版' in results else list(results.keys())[0]
            baseline_result = results[baseline_version]['results']
            
            if not baseline_result or baseline_result.get('status') != 'success':
                comparison['error'] = '基准结果无效'
                return comparison
            
            baseline_indicators = baseline_result.get('indicators', {})
            baseline_buy_signal = baseline_result.get('buy_signal', False)
            
            # 与其他版本比较
            for version, perf_data in results.items():
                if version == baseline_version:
                    continue
                
                result = perf_data['results']
                if not result or result.get('status') != 'success':
                    continue
                
                indicators = result.get('indicators', {})
                buy_signal = result.get('buy_signal', False)
                
                # 比较指标值
                for key in baseline_indicators:
                    if key in indicators:
                        baseline_val = baseline_indicators[key]
                        current_val = indicators[key]
                        
                        # 允许小的数值误差
                        if abs(baseline_val - current_val) > 1e-4:
                            comparison['accuracy_check'] = False
                            comparison['differences'].append(
                                f"{version} vs {baseline_version} - {key}: {current_val:.6f} vs {baseline_val:.6f}"
                            )
                
                # 比较信号
                if buy_signal != baseline_buy_signal:
                    comparison['signal_consistency'] = False
                    comparison['differences'].append(
                        f"{version} vs {baseline_version} - 买入信号: {buy_signal} vs {baseline_buy_signal}"
                    )
                    
        except Exception as e:
            comparison['error'] = str(e)
            
        return comparison
    
    def run_comprehensive_tests(self):
        """运行全面性能测试"""
        print("🚀 开始全面性能测试...")
        print("=" * 80)
        
        if not self.setup_detectors():
            print("❌ 无法初始化检测器，测试终止")
            return
        
        # 测试场景
        test_scenarios = [
            {'name': '小数据量', 'bars': 100, 'iterations': 8},
            {'name': '中数据量', 'bars': 300, 'iterations': 5},
            {'name': '大数据量', 'bars': 500, 'iterations': 3},
            {'name': '超大数据量', 'bars': 1000, 'iterations': 2}
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 测试场景: {scenario['name']} ({scenario['bars']}根K线, {scenario['iterations']}次迭代)")
            print("-" * 70)
            
            # 生成测试数据
            test_data = self.generate_test_data(scenario['bars'])
            
            # 测试所有版本
            scenario_results = {}
            
            for version, detector in self.detectors.items():
                print(f"🔄 测试 {version}...")
                perf_result = self.measure_performance_advanced(
                    detector, version, test_data, scenario['iterations']
                )
                scenario_results[version] = perf_result
                
                print(f"   平均耗时: {perf_result['avg_time']*1000:.2f}ms")
                print(f"   内存峰值: {perf_result['memory_peak']:.2f}MB")
                print(f"   成功率: {perf_result['success_rate']*100:.1f}%")
                
                # 显示v2.0的缓存信息
                if version == 'v2.0' and perf_result['performance_info']:
                    cache_hit_rate = perf_result['performance_info'].get('cache_hit_rate', 0)
                    print(f"   缓存命中率: {cache_hit_rate*100:.1f}%")
            
            # 性能对比分析
            if len(scenario_results) > 1:
                print(f"\n📈 性能对比分析:")
                
                # 找到最快的版本作为基准
                fastest_version = min(scenario_results.keys(), 
                                    key=lambda v: scenario_results[v]['avg_time'])
                fastest_time = scenario_results[fastest_version]['avg_time']
                
                for version, result in scenario_results.items():
                    speedup = fastest_time / result['avg_time'] if result['avg_time'] > 0 else 0
                    if version == fastest_version:
                        print(f"   {version}: 基准 (最快)")
                    else:
                        print(f"   {version}: {speedup:.2f}x 相对于最快版本")
                
                # 计算精度比较
                accuracy_check = self.compare_calculation_accuracy(scenario_results)
                if accuracy_check['accuracy_check'] and accuracy_check['signal_consistency']:
                    print(f"   ✅ 所有版本计算结果一致")
                else:
                    print(f"   ⚠️ 发现计算差异:")
                    for diff in accuracy_check['differences'][:3]:  # 只显示前3个
                        print(f"      {diff}")
            
            # 保存测试结果
            self.test_results[scenario['name']] = scenario_results
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终测试报告"""
        if not self.test_results:
            print("\n⚠️ 没有测试结果可报告")
            return
            
        print("\n" + "=" * 80)
        print("📋 全面性能测试报告")
        print("=" * 80)
        
        # 统计各版本的平均性能
        version_stats = {}
        
        for scenario_name, scenario_results in self.test_results.items():
            for version, result in scenario_results.items():
                if version not in version_stats:
                    version_stats[version] = {
                        'avg_times': [],
                        'memory_peaks': [],
                        'success_rates': []
                    }
                
                version_stats[version]['avg_times'].append(result['avg_time'])
                version_stats[version]['memory_peaks'].append(result['memory_peak'])
                version_stats[version]['success_rates'].append(result['success_rate'])
        
        # 计算总体统计
        print(f"\n🎯 各版本总体性能:")
        for version, stats in version_stats.items():
            avg_time = np.mean(stats['avg_times'])
            avg_memory = np.mean(stats['memory_peaks'])
            avg_success = np.mean(stats['success_rates'])
            
            print(f"\n{version}:")
            print(f"  平均耗时: {avg_time*1000:.2f}ms")
            print(f"  平均内存: {avg_memory:.2f}MB")
            print(f"  平均成功率: {avg_success*100:.1f}%")
        
        # 性能提升分析
        if '原版' in version_stats:
            baseline_time = np.mean(version_stats['原版']['avg_times'])
            
            print(f"\n🚀 相对于原版的性能提升:")
            for version, stats in version_stats.items():
                if version == '原版':
                    continue
                
                avg_time = np.mean(stats['avg_times'])
                speedup = baseline_time / avg_time if avg_time > 0 else 0
                
                print(f"  {version}: {speedup:.2f}x 提升")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        if 'v2.0' in version_stats:
            print(f"  ✅ v2.0版本已实现统一指标引擎和智能缓存")
            print(f"  🔄 建议继续优化：增量计算、并行处理")
        else:
            print(f"  📋 建议实现：统一指标引擎、智能缓存系统")
        
        print(f"\n🎉 测试完成！共测试了 {len(version_stats)} 个版本，{len(self.test_results)} 个场景")

if __name__ == "__main__":
    # 运行全面性能测试
    test_suite = ComprehensivePerformanceTest()
    test_suite.run_comprehensive_tests()
