#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号检测测试脚本
用于诊断QMT兼容版信号检测失败问题
"""

import sys
import os
import numpy as np
import traceback

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_klines(count=100):
    """创建测试K线数据"""
    klines = []
    base_price = 10.0
    
    for i in range(count):
        # 模拟价格波动
        price_change = np.random.normal(0, 0.02)
        base_price = max(1.0, base_price * (1 + price_change))
        
        # 生成OHLC数据
        open_price = base_price
        high_price = open_price * (1 + abs(np.random.normal(0, 0.01)))
        low_price = open_price * (1 - abs(np.random.normal(0, 0.01)))
        close_price = low_price + (high_price - low_price) * np.random.random()
        volume = int(np.random.uniform(1000, 10000))
        
        kline = {
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume,
            'time': f"2024-01-{i+1:02d} 09:30:00"
        }
        klines.append(kline)
    
    return klines

def test_signal_detection():
    """测试信号检测功能"""
    try:
        print("🔍 开始信号检测测试...")
        
        # 导入检测器类
        import importlib.util
        spec = importlib.util.spec_from_file_location("qmt_strategy", "6sk线_QMT兼容版.py")
        qmt_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(qmt_module)

        CompleteCMFBIASDivergenceDetector = qmt_module.CompleteCMFBIASDivergenceDetector
        
        # 创建检测器实例
        detector = CompleteCMFBIASDivergenceDetector(
            SKDJ_N=8, SKDJ_M=4,
            CMF_N=30, CMF_M=20,
            BIAS_N=30, BIAS_M=20,
            ADX_N=23, ADX_M=11,
            VAE_BASE_TR=1.8,
            VAE_INITIAL_STOP=1.2,  # 用户修改的值
            VAE_PERIOD=20
        )
        
        print("✅ 检测器创建成功")
        
        # 创建测试数据
        test_klines = create_test_klines(100)
        print(f"✅ 测试数据创建成功: {len(test_klines)}根K线")
        
        # 测试各个指标计算
        print("\n🧪 测试各个指标计算...")
        
        # 1. 测试SKDJ
        try:
            k_vals, d_vals = detector.calculate_skdj(test_klines, 8, 4)
            print(f"✅ SKDJ计算成功: K={k_vals[-1]:.2f}, D={d_vals[-1]:.2f}")
        except Exception as e:
            print(f"❌ SKDJ计算失败: {e}")
            traceback.print_exc()
        
        # 2. 测试CMF
        try:
            cmf_vals = detector.calculate_cmf(test_klines, 30)
            print(f"✅ CMF计算成功: {cmf_vals[-1]:.4f}")
        except Exception as e:
            print(f"❌ CMF计算失败: {e}")
            traceback.print_exc()
        
        # 3. 测试BIAS
        try:
            bias_vals = detector.calculate_bias(test_klines, 30)
            print(f"✅ BIAS计算成功: {bias_vals[-1]:.4f}")
        except Exception as e:
            print(f"❌ BIAS计算失败: {e}")
            traceback.print_exc()
        
        # 4. 测试ADX
        try:
            adx_vals = detector.calculate_adx(test_klines, 23)
            print(f"✅ ADX计算成功: {adx_vals[-1]:.2f}")
        except Exception as e:
            print(f"❌ ADX计算失败: {e}")
            traceback.print_exc()
        
        # 5. 测试VAE
        try:
            vae_info = detector.calculate_vae_dynamic_control(test_klines)
            print(f"✅ VAE计算成功: {vae_info['volatility_zone']}")
        except Exception as e:
            print(f"❌ VAE计算失败: {e}")
            traceback.print_exc()
        
        # 6. 测试完整信号检测
        print("\n🎯 测试完整信号检测...")
        try:
            signal_result = detector.get_signals(test_klines)
            print(f"✅ 信号检测成功!")
            print(f"   状态: {signal_result['status']}")
            print(f"   买入信号: {signal_result['buy_signal']}")
            print(f"   消息: {signal_result.get('message', 'N/A')}")
            
            if signal_result['status'] == 'error':
                print(f"❌ 错误详情: {signal_result.get('error_message', '未知')}")
                if 'error_details' in signal_result:
                    print(f"📋 详细错误:\n{signal_result['error_details']}")
            
        except Exception as e:
            print(f"❌ 信号检测异常: {e}")
            traceback.print_exc()
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_signal_detection()
