"""
纯pandas技术指标库 - 第三阶段优化
========================================================================

目标：
1. 完全基于pandas/numpy实现所有技术指标
2. 消除talib依赖，提升性能和可控性
3. 向量化计算，支持大批量数据处理
4. 内存优化，支持更大数据量

预期性能提升：30-50%（相比talib）
"""

import numpy as np
import pandas as pd
from typing import Union, Tuple
import warnings

class PandasIndicators:
    """基于pandas的高性能技术指标库"""
    
    @staticmethod
    def sma(series: pd.Series, period: int, min_periods: int = 1) -> pd.Series:
        """
        简单移动平均线 - pandas优化版
        
        优势：
        1. 纯pandas实现，无外部依赖
        2. 自动处理NaN值
        3. 支持min_periods参数
        """
        return series.rolling(window=period, min_periods=min_periods).mean()
    
    @staticmethod
    def ema(series: pd.Series, period: int, adjust: bool = False) -> pd.Series:
        """
        指数移动平均线 - pandas优化版
        
        优势：
        1. 使用pandas内置ewm方法
        2. 性能优于talib实现
        3. 支持adjust参数控制
        """
        return series.ewm(span=period, adjust=adjust).mean()
    
    @staticmethod
    def max_rolling(series: pd.Series, period: int) -> pd.Series:
        """滚动最大值 - pandas优化版"""
        return series.rolling(window=period, min_periods=1).max()
    
    @staticmethod
    def min_rolling(series: pd.Series, period: int) -> pd.Series:
        """滚动最小值 - pandas优化版"""
        return series.rolling(window=period, min_periods=1).min()
    
    @staticmethod
    def sum_rolling(series: pd.Series, period: int) -> pd.Series:
        """滚动求和 - pandas优化版"""
        return series.rolling(window=period, min_periods=1).sum()
    
    @staticmethod
    def skdj(df: pd.DataFrame, n: int = 8, m: int = 4) -> Tuple[pd.Series, pd.Series]:
        """
        SKDJ指标 - 纯pandas实现
        
        优化点：
        1. 向量化计算RSV
        2. 使用pandas ewm计算平滑
        3. 避免循环操作
        """
        highs = df['high']
        lows = df['low']
        closes = df['close']
        
        # 计算LOWV和HIGHV
        lowv = lows.rolling(window=n, min_periods=1).min()
        highv = highs.rolling(window=n, min_periods=1).max()
        
        # 向量化RSV计算
        denominator = highv - lowv
        denominator = denominator.replace(0, np.nan)  # 避免除零
        rsv_raw = (closes - lowv) / denominator * 100
        rsv_raw = rsv_raw.fillna(50.0)  # 填充NaN为50
        
        # 使用EMA平滑
        rsv = rsv_raw.ewm(span=m, adjust=False).mean()
        k = rsv.ewm(span=m, adjust=False).mean()
        d = k.rolling(window=m, min_periods=1).mean()
        
        return k, d
    
    @staticmethod
    def cmf(df: pd.DataFrame, period: int = 30) -> pd.Series:
        """
        Chaikin Money Flow - 纯pandas实现
        
        优化点：
        1. 向量化CLV计算
        2. 使用pandas rolling操作
        3. 高效的除零处理
        """
        highs = df['high']
        lows = df['low']
        closes = df['close']
        volumes = df['volume']
        
        # 向量化CLV计算
        denominator = highs - lows
        denominator = denominator.replace(0, 1e-8)  # 避免除零
        clv = (closes - lows - highs + closes) / denominator
        clv = clv.fillna(0.0)
        
        # MF计算
        mf = clv * volumes
        
        # 滚动求和
        mf_sum = mf.rolling(window=period, min_periods=1).sum()
        vol_sum = volumes.rolling(window=period, min_periods=1).sum()
        
        # CMF计算
        vol_sum = vol_sum.replace(0, 1e-8)  # 避免除零
        cmf = mf_sum / vol_sum
        
        return cmf.fillna(0.0)
    
    @staticmethod
    def bias(df: pd.DataFrame, period: int = 30) -> pd.Series:
        """
        BIAS乖离率 - 纯pandas实现
        
        优化点：
        1. 直接使用pandas rolling mean
        2. 向量化百分比计算
        3. 高效的异常值处理
        """
        closes = df['close']
        
        # 计算移动平均
        ma_close = closes.rolling(window=period, min_periods=1).mean()
        
        # 向量化BIAS计算
        ma_close = ma_close.replace(0, 1e-8)  # 避免除零
        bias = (closes - ma_close) / ma_close * 100
        
        # 处理异常值
        bias = bias.replace([np.inf, -np.inf], 0.0)
        return bias.fillna(0.0)
    
    @staticmethod
    def atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
        """
        Average True Range - 纯pandas实现
        
        优化点：
        1. 向量化TR计算
        2. 使用pandas ewm计算ATR
        3. 高效的数据对齐
        """
        highs = df['high']
        lows = df['low']
        closes = df['close']
        
        # 向量化TR计算
        prev_close = closes.shift(1)
        
        tr1 = highs - lows
        tr2 = (highs - prev_close).abs()
        tr3 = (lows - prev_close).abs()
        
        # 取最大值
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 第一个值特殊处理
        tr.iloc[0] = tr1.iloc[0]
        
        # 使用EMA计算ATR
        atr = tr.ewm(span=period, adjust=False).mean()
        
        return atr.fillna(0.0)
    
    @staticmethod
    def adx_pure_pandas(df: pd.DataFrame, period: int = 14, smooth_period: int = 14) -> pd.Series:
        """
        ADX指标 - 完全pandas实现
        
        优化点：
        1. 完全向量化计算
        2. 消除所有循环
        3. 使用pandas高效操作
        4. 内存优化
        """
        highs = df['high']
        lows = df['low']
        closes = df['close']
        
        # 向量化TR计算
        prev_close = closes.shift(1)
        
        hl = highs - lows
        hc = (highs - prev_close).abs()
        lc = (lows - prev_close).abs()
        
        tr = pd.concat([hl, hc, lc], axis=1).max(axis=1)
        tr.iloc[0] = hl.iloc[0]  # 第一个值特殊处理
        
        # MTR计算
        mtr = tr.rolling(window=period, min_periods=1).sum()
        
        # 向量化HD/LD计算
        hd = highs.diff()
        ld = lows.diff() * -1  # 注意符号
        
        # 第一个值设为0
        hd.iloc[0] = 0.0
        ld.iloc[0] = 0.0
        
        # 向量化DMP/DMM计算
        dmp_condition = (hd > 0) & (hd > ld)
        dmm_condition = (ld > 0) & (ld > hd)
        
        dmp_values = pd.Series(np.where(dmp_condition, hd, 0.0), index=df.index)
        dmm_values = pd.Series(np.where(dmm_condition, ld, 0.0), index=df.index)
        
        # DMP/DMM求和
        dmp = dmp_values.rolling(window=period, min_periods=1).sum()
        dmm = dmm_values.rolling(window=period, min_periods=1).sum()
        
        # 避免除零
        mtr = mtr.replace(0, 1e-8)
        
        # PDI/MDI计算
        pdi = dmp * 100.0 / mtr
        mdi = dmm * 100.0 / mtr
        
        # DX计算
        dx_numerator = (mdi - pdi).abs()
        dx_denominator = mdi + pdi
        dx_denominator = dx_denominator.replace(0, 1e-8)
        dx = dx_numerator / dx_denominator * 100.0
        
        # ADX计算 - 使用SMA
        adx = dx.rolling(window=smooth_period, min_periods=1).mean()
        
        # 处理异常值
        adx = adx.replace([np.inf, -np.inf], 0.0)
        return adx.fillna(0.0)

class IndicatorPipeline:
    """
    指标计算流水线
    
    特性：
    1. 链式调用
    2. 批量计算
    3. 结果缓存
    4. 性能监控
    """
    
    def __init__(self, df: pd.DataFrame):
        """初始化流水线"""
        self.df = df.copy()
        self.results = {}
        self.computation_time = {}
        
    def add_sma(self, periods: list, column: str = 'close') -> 'IndicatorPipeline':
        """添加SMA计算"""
        import time
        start_time = time.perf_counter()
        
        for period in periods:
            key = f'SMA_{period}'
            self.results[key] = PandasIndicators.sma(self.df[column], period)
        
        self.computation_time['SMA'] = time.perf_counter() - start_time
        return self
    
    def add_ema(self, periods: list, column: str = 'close') -> 'IndicatorPipeline':
        """添加EMA计算"""
        import time
        start_time = time.perf_counter()
        
        for period in periods:
            key = f'EMA_{period}'
            self.results[key] = PandasIndicators.ema(self.df[column], period)
        
        self.computation_time['EMA'] = time.perf_counter() - start_time
        return self
    
    def add_skdj(self, n: int = 8, m: int = 4) -> 'IndicatorPipeline':
        """添加SKDJ计算"""
        import time
        start_time = time.perf_counter()
        
        k, d = PandasIndicators.skdj(self.df, n, m)
        self.results['SKDJ_K'] = k
        self.results['SKDJ_D'] = d
        
        self.computation_time['SKDJ'] = time.perf_counter() - start_time
        return self
    
    def add_cmf(self, period: int = 30) -> 'IndicatorPipeline':
        """添加CMF计算"""
        import time
        start_time = time.perf_counter()
        
        self.results['CMF'] = PandasIndicators.cmf(self.df, period)
        
        self.computation_time['CMF'] = time.perf_counter() - start_time
        return self
    
    def add_bias(self, period: int = 30) -> 'IndicatorPipeline':
        """添加BIAS计算"""
        import time
        start_time = time.perf_counter()
        
        self.results['BIAS'] = PandasIndicators.bias(self.df, period)
        
        self.computation_time['BIAS'] = time.perf_counter() - start_time
        return self
    
    def add_adx(self, period: int = 23, smooth_period: int = 11) -> 'IndicatorPipeline':
        """添加ADX计算"""
        import time
        start_time = time.perf_counter()
        
        self.results['ADX'] = PandasIndicators.adx_pure_pandas(self.df, period, smooth_period)
        
        self.computation_time['ADX'] = time.perf_counter() - start_time
        return self
    
    def add_atr(self, period: int = 20) -> 'IndicatorPipeline':
        """添加ATR计算"""
        import time
        start_time = time.perf_counter()
        
        self.results['ATR'] = PandasIndicators.atr(self.df, period)
        
        self.computation_time['ATR'] = time.perf_counter() - start_time
        return self
    
    def execute(self) -> dict:
        """执行流水线并返回结果"""
        total_time = sum(self.computation_time.values())
        
        return {
            'indicators': self.results,
            'computation_time': self.computation_time,
            'total_time': total_time,
            'data_length': len(self.df),
            'indicators_count': len(self.results)
        }
    
    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        total_time = sum(self.computation_time.values())
        
        return {
            'total_computation_time': total_time,
            'average_time_per_indicator': total_time / max(1, len(self.computation_time)),
            'time_breakdown': self.computation_time,
            'data_points_processed': len(self.df),
            'processing_rate': len(self.df) / max(total_time, 1e-6)  # 数据点/秒
        }
