"""
增量计算引擎 - 第三阶段优化
========================================================================

目标：
1. 只计算新增K线数据，复用历史计算结果
2. 智能缓存管理，自动清理过期数据
3. 支持实盘场景的高频更新
4. 内存优化，支持长期运行

预期性能提升：50-80%（实盘场景）
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import time
from collections import deque
from dataclasses import dataclass
import hashlib

@dataclass
class IncrementalState:
    """增量计算状态"""
    last_data_hash: str = ""
    last_data_length: int = 0
    cached_indicators: Dict[str, pd.Series] = None
    computation_stats: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.cached_indicators is None:
            self.cached_indicators = {}
        if self.computation_stats is None:
            self.computation_stats = {
                'total_updates': 0,
                'incremental_updates': 0,
                'full_recalculations': 0,
                'cache_hits': 0,
                'avg_update_time': 0.0
            }

class IncrementalDataManager:
    """增量数据管理器"""
    
    def __init__(self, max_history: int = 1000, lookback_buffer: int = 100):
        """
        初始化增量数据管理器
        
        Args:
            max_history: 最大历史数据保留量
            lookback_buffer: 回看缓冲区大小（用于指标计算）
        """
        self.max_history = max_history
        self.lookback_buffer = lookback_buffer
        self.historical_data = pd.DataFrame()
        self.state = IncrementalState()
        
    def _calculate_data_hash(self, df: pd.DataFrame) -> str:
        """计算数据哈希值"""
        # 使用最后几行数据计算哈希，快速检测变化
        last_rows = df.tail(min(10, len(df)))
        data_str = last_rows.to_string()
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def _determine_update_strategy(self, new_df: pd.DataFrame) -> Tuple[str, int, int]:
        """
        确定更新策略
        
        Returns:
            strategy: 'full' | 'incremental' | 'append'
            start_index: 开始计算的索引
            new_data_count: 新数据数量
        """
        if self.historical_data.empty:
            return 'full', 0, len(new_df)
        
        current_hash = self._calculate_data_hash(new_df)
        
        # 检查数据是否有变化
        if current_hash == self.state.last_data_hash:
            return 'no_change', 0, 0
        
        # 检查是否为简单追加
        if len(new_df) > self.state.last_data_length:
            # 验证历史数据是否一致
            historical_part = new_df.iloc[:self.state.last_data_length]
            if len(self.historical_data) > 0:
                # 简单比较最后几行
                last_historical = self.historical_data.tail(min(5, len(self.historical_data)))
                last_new = historical_part.tail(min(5, len(historical_part)))
                
                if len(last_historical) == len(last_new):
                    # 比较关键列
                    key_columns = ['close', 'high', 'low', 'volume']
                    is_consistent = True
                    
                    for col in key_columns:
                        if col in last_historical.columns and col in last_new.columns:
                            if not np.allclose(last_historical[col].values, 
                                             last_new[col].values, 
                                             rtol=1e-6, atol=1e-8):
                                is_consistent = False
                                break
                    
                    if is_consistent:
                        new_data_count = len(new_df) - self.state.last_data_length
                        # 需要重新计算的起始位置（包含回看缓冲区）
                        start_index = max(0, self.state.last_data_length - self.lookback_buffer)
                        return 'incremental', start_index, new_data_count
        
        # 数据结构发生变化，需要完全重新计算
        return 'full', 0, len(new_df)
    
    def update_data(self, new_klines: List[Dict]) -> Tuple[pd.DataFrame, str, Dict]:
        """
        更新数据并返回需要计算的部分
        
        Returns:
            calculation_df: 需要计算的数据
            strategy: 更新策略
            update_info: 更新信息
        """
        # 转换为DataFrame
        new_df = pd.DataFrame(new_klines)
        
        # 确保数据类型
        ohlcv_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in ohlcv_columns:
            if col in new_df.columns:
                new_df[col] = pd.to_numeric(new_df[col], errors='coerce')
            else:
                new_df[col] = 0.0
        
        # 填充NaN值
        new_df[ohlcv_columns] = new_df[ohlcv_columns].fillna(method='ffill').fillna(0)
        
        # 确定更新策略
        strategy, start_index, new_data_count = self._determine_update_strategy(new_df)
        
        update_info = {
            'strategy': strategy,
            'start_index': start_index,
            'new_data_count': new_data_count,
            'total_data_length': len(new_df),
            'historical_length': len(self.historical_data)
        }
        
        if strategy == 'no_change':
            return self.historical_data, strategy, update_info
        
        elif strategy == 'incremental':
            # 增量更新：从start_index开始的数据
            calculation_df = new_df.iloc[start_index:].copy()
            update_info['calculation_length'] = len(calculation_df)
            
        elif strategy == 'full':
            # 完全重新计算
            calculation_df = new_df.copy()
            update_info['calculation_length'] = len(calculation_df)
            
        else:  # append
            # 简单追加（目前与incremental相同处理）
            calculation_df = new_df.iloc[start_index:].copy()
            update_info['calculation_length'] = len(calculation_df)
        
        # 更新历史数据
        self.historical_data = new_df.copy()
        
        # 限制历史数据大小
        if len(self.historical_data) > self.max_history:
            self.historical_data = self.historical_data.tail(self.max_history)
        
        # 更新状态
        self.state.last_data_hash = self._calculate_data_hash(new_df)
        self.state.last_data_length = len(new_df)
        
        return calculation_df, strategy, update_info

class IncrementalIndicatorEngine:
    """增量指标计算引擎"""
    
    def __init__(self, max_history: int = 1000, lookback_buffer: int = 100):
        """初始化增量指标引擎"""
        self.data_manager = IncrementalDataManager(max_history, lookback_buffer)
        self.indicator_cache = {}
        self.performance_stats = {
            'total_calculations': 0,
            'incremental_calculations': 0,
            'full_calculations': 0,
            'cache_efficiency': 0.0,
            'avg_calculation_time': 0.0,
            'total_time': 0.0
        }
        
    def _merge_incremental_results(self, 
                                  existing_indicators: Dict[str, pd.Series], 
                                  new_indicators: Dict[str, pd.Series],
                                  strategy: str,
                                  start_index: int) -> Dict[str, pd.Series]:
        """合并增量计算结果"""
        
        if strategy == 'full' or not existing_indicators:
            return new_indicators
        
        merged_indicators = {}
        
        for indicator_name, new_values in new_indicators.items():
            if indicator_name in existing_indicators:
                existing_values = existing_indicators[indicator_name]
                
                if strategy == 'incremental':
                    # 增量合并：保留历史部分，更新新部分
                    if start_index > 0:
                        # 保留start_index之前的历史数据
                        historical_part = existing_values.iloc[:start_index]
                        # 合并新计算的数据
                        merged_values = pd.concat([historical_part, new_values])
                    else:
                        merged_values = new_values
                else:
                    # 其他策略直接使用新值
                    merged_values = new_values
                
                merged_indicators[indicator_name] = merged_values
            else:
                merged_indicators[indicator_name] = new_values
        
        return merged_indicators
    
    def calculate_indicators_incremental(self, 
                                       new_klines: List[Dict],
                                       indicator_configs: Dict) -> Dict[str, Any]:
        """
        增量计算指标
        
        Args:
            new_klines: 新的K线数据
            indicator_configs: 指标配置
            
        Returns:
            计算结果和性能信息
        """
        start_time = time.perf_counter()
        
        # 更新数据并获取计算策略
        calculation_df, strategy, update_info = self.data_manager.update_data(new_klines)
        
        if strategy == 'no_change':
            # 数据无变化，直接返回缓存结果
            return {
                'indicators': self.indicator_cache.copy(),
                'strategy': strategy,
                'update_info': update_info,
                'computation_time': time.perf_counter() - start_time,
                'cache_hit': True
            }
        
        # 导入pandas指标库
        from 纯pandas指标库 import IndicatorPipeline
        
        # 创建指标计算流水线
        pipeline = IndicatorPipeline(calculation_df)
        
        # 根据配置添加指标
        if 'SKDJ' in indicator_configs:
            config = indicator_configs['SKDJ']
            pipeline.add_skdj(config.get('n', 8), config.get('m', 4))
        
        if 'CMF' in indicator_configs:
            config = indicator_configs['CMF']
            pipeline.add_cmf(config.get('period', 30))
        
        if 'BIAS' in indicator_configs:
            config = indicator_configs['BIAS']
            pipeline.add_bias(config.get('period', 30))
        
        if 'ADX' in indicator_configs:
            config = indicator_configs['ADX']
            pipeline.add_adx(config.get('period', 23), config.get('smooth_period', 11))
        
        if 'ATR' in indicator_configs:
            config = indicator_configs['ATR']
            pipeline.add_atr(config.get('period', 20))
        
        if 'SMA' in indicator_configs:
            config = indicator_configs['SMA']
            periods = config.get('periods', [20, 30])
            pipeline.add_sma(periods)
        
        # 执行计算
        pipeline_result = pipeline.execute()
        new_indicators = pipeline_result['indicators']
        
        # 合并增量结果
        if strategy == 'incremental':
            merged_indicators = self._merge_incremental_results(
                self.indicator_cache, 
                new_indicators, 
                strategy, 
                update_info['start_index']
            )
        else:
            merged_indicators = new_indicators
        
        # 更新缓存
        self.indicator_cache = merged_indicators
        
        # 更新性能统计
        computation_time = time.perf_counter() - start_time
        self.performance_stats['total_calculations'] += 1
        self.performance_stats['total_time'] += computation_time
        
        if strategy == 'incremental':
            self.performance_stats['incremental_calculations'] += 1
        else:
            self.performance_stats['full_calculations'] += 1
        
        self.performance_stats['avg_calculation_time'] = (
            self.performance_stats['total_time'] / 
            self.performance_stats['total_calculations']
        )
        
        # 计算缓存效率
        total_calcs = self.performance_stats['total_calculations']
        incremental_calcs = self.performance_stats['incremental_calculations']
        self.performance_stats['cache_efficiency'] = incremental_calcs / max(total_calcs, 1)
        
        return {
            'indicators': merged_indicators,
            'strategy': strategy,
            'update_info': update_info,
            'computation_time': computation_time,
            'pipeline_stats': pipeline_result,
            'performance_stats': self.performance_stats.copy(),
            'cache_hit': False
        }
    
    def get_latest_values(self, indicator_names: List[str] = None) -> Dict[str, float]:
        """获取最新的指标值"""
        if not self.indicator_cache:
            return {}
        
        latest_values = {}
        
        if indicator_names is None:
            indicator_names = list(self.indicator_cache.keys())
        
        for name in indicator_names:
            if name in self.indicator_cache:
                series = self.indicator_cache[name]
                if len(series) > 0:
                    latest_values[name] = series.iloc[-1]
                else:
                    latest_values[name] = 0.0
            else:
                latest_values[name] = 0.0
        
        return latest_values
    
    def clear_cache(self):
        """清理缓存"""
        self.indicator_cache.clear()
        self.data_manager.historical_data = pd.DataFrame()
        self.data_manager.state = IncrementalState()
        
        # 重置性能统计
        self.performance_stats = {
            'total_calculations': 0,
            'incremental_calculations': 0,
            'full_calculations': 0,
            'cache_efficiency': 0.0,
            'avg_calculation_time': 0.0,
            'total_time': 0.0
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_calcs = self.performance_stats['total_calculations']
        
        return {
            'total_calculations': total_calcs,
            'incremental_ratio': self.performance_stats['cache_efficiency'],
            'avg_calculation_time': self.performance_stats['avg_calculation_time'],
            'total_time_saved': self.performance_stats['total_time'],
            'efficiency_rating': 'High' if self.performance_stats['cache_efficiency'] > 0.7 else 
                               'Medium' if self.performance_stats['cache_efficiency'] > 0.4 else 'Low'
        }
