{优化后完整交易策略方案}

{一、策略核心架构}
本策略基于多重背离确认 + 趋势强度过滤 + 动态风控的三层架构：

第一层：背离信号识别（CMF + BIAS双重背离）
第二层：市场环境过滤（SKDJ超卖 + ADX强趋势）
第三层：精准入场时机（阻力线突破确认）
第四层：动态风险管理（VAE自适应止盈止损）

{二、完整策略代码实现}

{参数配置区}
SKDJ_N:8;    SKDJ_M:4;     {SKDJ超卖判断}
CMF_N:30;    CMF_M:20;     {资金流背离}
BIAS_N:30;   BIAS_M:20;    {乖离率背离}
ADX_N:23;    ADX_M:11;     {趋势强度过滤}
VAE_基础TR:1.8; VAE_初始止损:1.5; VAE_周期:20; {动态风控}
固定止损:0.5; {固定止损百分比}

{基础计算模块}
{ATR波动率}
当前ATR:=ATR(VAE_周期);
ATR均值:=MA(当前ATR,VAE_周期*2);

{SKDJ超卖指标}
LOWV:=LLV(LOW,SKDJ_N);
HIGHV:=HHV(HIGH,SKDJ_N);
RSV:=EMA((CLOSE-LOWV)/(HIGHV-LOWV)*100,SKDJ_M);
K:=EMA(RSV,SKDJ_M);
D:=MA(K,SKDJ_M);

{CMF资金流背离}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);
MF:=CLV*VOL;
CMF:=SUM(MF,CMF_N)/SUM(VOL,CMF_N);
CMF_HH:=HIGH>=HHV(HIGH,CMF_M);
CMF_LL:=LOW<=LLV(LOW,CMF_M);
CMF_指标HH:=CMF>=HHV(CMF,CMF_M);
CMF_指标LL:=CMF<=LLV(CMF,CMF_M);
CMF顶背离:=CMF_HH AND CMF_指标HH=0 AND CMF>0;
CMF底背离:=CMF_LL AND CMF_指标LL=0 AND CMF<0;

{BIAS乖离率背离}
BIAS:=(CLOSE-MA(CLOSE,BIAS_N))/MA(CLOSE,BIAS_N)*100;
BIAS_HH:=HIGH>=HHV(HIGH,BIAS_M);
BIAS_LL:=LOW<=LLV(LOW,BIAS_M);
BIAS_指标HH:=BIAS>=HHV(BIAS,BIAS_M);
BIAS_指标LL:=BIAS<=LLV(BIAS,BIAS_M);
BIAS顶背离:=BIAS_HH AND BIAS_指标HH=0 AND BIAS>0;
BIAS底背离:=BIAS_LL AND BIAS_指标LL=0 AND BIAS<0;

{ADX趋势强度}
MTR:=SUM(MAX(MAX(HIGH-LOW,ABS(HIGH-REF(CLOSE,1))),ABS(REF(CLOSE,1)-LOW)),ADX_N);
HD:=HIGH-REF(HIGH,1);
LD:=REF(LOW,1)-LOW;
DMP:=SUM(IF(HD>0&&HD>LD,HD,0),ADX_N);
DMM:=SUM(IF(LD>0&&LD>HD,LD,0),ADX_N);
PDI:=DMP*100/MTR;
MDI:=DMM*100/MTR;
ADX:=MA(ABS(MDI-PDI)/(MDI+PDI)*100,ADX_M);

{阻力线突破}
K线加权均值:=(HIGH+LOW+2*CLOSE)/4;
阻力线:=K线加权均值+(K线加权均值-LOW);
突破条件:=CLOSE>阻力线 AND REF(CLOSE,1)<=REF(阻力线,1);

{VAE动态风控}
波动率比值:=当前ATR/ATR均值;
低波动区:=波动率比值<=0.8;
正常波动区:=波动率比值>0.8 AND 波动率比值<=1.2;
高波动区:=波动率比值>1.2 AND 波动率比值<=1.8;
极高波动区:=波动率比值>1.8;
TR1:=IF(低波动区,VAE_基础TR*2,VAE_基础TR);
TR2:=IF(高波动区,VAE_基础TR*1,TR1);
动态TR:=IF(极高波动区,VAE_基础TR*0.7,TR2);
多头止盈基准:=CLOSE-当前ATR*动态TR;
多头止盈线:=HHV(多头止盈基准,3);

{交易信号生成}
{买入条件组合}
SKDJ超卖:=K<20 AND D<20;
双重背离:=(CMF底背离 OR REF(CMF底背离,1) OR REF(CMF底背离,2)) AND
          (BIAS底背离 OR REF(BIAS底背离,1) OR REF(BIAS底背离,2));
强趋势确认:=ADX>40;
突破确认:=突破条件;

买入信号:=SKDJ超卖 AND 双重背离 AND 强趋势确认 AND 突破确认;

{卖出条件组合}
固定止损信号:=(CLOSE-COST)/COST<-固定止损/100;
动态止盈信号:=CLOSE<=多头止盈线;

卖出信号:=固定止损信号 OR 动态止盈信号;

{三、策略优势与特色}

{1. 多重确认机制}
- 双重背离确认：CMF + BIAS同时背离，提高信号可靠性
- 时间窗口容错：允许前1-2根K线的背离信号，避免错过机会
- 趋势环境过滤：ADX>40确保在强趋势中交易，避免震荡市假信号

{2. 精准入场时机}
- SKDJ超卖确认：K<20且D<20，确保在超卖区域入场
- 阻力线突破：刚刚突破阻力线时入场，避免追高风险
- 多层过滤机制：5个条件同时满足才产生买入信号

{3. 智能风险管理}
- 固定止损保护：0.5%固定止损，控制单笔最大亏损
- VAE动态止盈：根据市场波动率自适应调整止盈位
- 波动率分区管理：低波动放宽止盈，高波动收紧止盈

{四、实施路线图}

{第一阶段：代码实现（1-2天）}
1. 将完整策略代码整合到交易软件
2. 进行语法检查和基础测试
3. 确认所有指标计算正确
4. 验证买卖信号逻辑

{第二阶段：历史回测（3-5天）}
1. 选择代表性时间段进行回测
2. 分析不同市场环境下的表现
3. 统计胜率、盈亏比、最大回撤等关键指标
4. 识别策略的优势时段和弱势时段


{定性优势}
- 适应性强：VAE模块自动适应市场波动
- 信号质量高：多重过滤减少假信号
- 风险可控：固定+动态双重止损机制
- 逻辑清晰：每个组件都有明确的作用

